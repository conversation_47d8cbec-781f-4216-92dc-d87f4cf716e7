package org.ichart.demo.module.ichartform.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.ichart.demo.module.ichartform.dao.ReportDao;
import org.ichart.demo.module.ichartform.model.entity.Report;
import org.ichart.demo.module.ichartform.model.entity.RptReport;
import org.ichart.demo.module.ichartform.service.ReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("reportService")
public class ReportServiceImpl extends ServiceImpl<ReportDao, Report> implements ReportService {
    private static final Logger logger = LoggerFactory.getLogger(ReportServiceImpl.class);

    @Autowired
    private ReportDao reportDao;
//    @Autowired
//    private TaskDao taskDao;
//    @Autowired
//    private ProjectDao projectDao;
//    @Autowired
//    private ExamineService examineService;
//    @Autowired
//    private ExamineChainService examineChainService;
//    @Autowired
//    @Lazy
//    private PMSendMailService pmSendMailService;
//    @Autowired
//    private TaskCycleDao taskCycleDao;
//    @Autowired
//    private TaskService taskService;
//    @Autowired
//    private ReportJoinService reportJoinService;
//    @Autowired
//    private UserUtil userUtil;
//
//    @Override
//    public PageUtils queryPage(Map<String, Object> params) {
//        Long projectId = MapUtils.getLong(params, "project_id");
//        IPage<Report> page = this.baseMapper.selectPage(
//                new Query<Report>(params).getPage(),
//                new QueryWrapper<Report>().eq(projectId != null, "project_id", projectId).
//                        eq("tenant_id", MapUtils.getLong(params, "tenantId"))
//        );
//
//        return new PageUtils(page);
//    }
//
//    @Override
//    public List<Report> queryReportList(Map params) {
//        return reportDao.queryReportList(params);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void saveReport(Report report) {
//        if(StringUtils.isNotBlank(report.getRemark())){
//            updateOverdueRemark(report);
//        }
//        if (report.getStatus() == ReportStatus.UNAUDITED.getValue()) {
//            report.setVersion(1);
//        }
//        report.setCreater(userUtil.getUserId());
//        report.setCreateTime(new Date());
//        report.setReportTime(new Date());
////        report.setUpdateTime(new Date());
//        Task task = taskDao.selectById(report.getTaskId());
//        report.setTaskName(task.getTaskName());
//        report.setProjectId(task.getProjectId());
//        this.baseMapper.insert(report);
//        if (report.getStatus() == ReportStatus.UNAUDITED.getValue()) { // 待审核
//            Project project = projectDao.selectById(report.getProjectId());
//            examineService.insertChainRecord(project, report);
//            //更新填报进度
//            updateReportPercent(report);
//        }
//        // 更新任务、项目的逾期状态
//
//        // 更新任务的填报统计信息
//        taskService.updateTaskFillInInfo(report.getTaskId());
//        //记录更新到reportJoin表
//        insertReportJoin(report);
//    }
//
//    @Override
//    public List<Map<String, Integer>> staticsSpendHrs(String projectId) {
//        return reportDao.staticsSpendHrs(projectId);
//    }
//
//    public void updateOverdueRemark(Report report){
//        TaskCycle taskCycle = new TaskCycle();
//        taskCycle.setId(report.getReportCycleId());
//        taskCycle.setRemark(report.getRemark());
//        taskCycleDao.updateById(taskCycle);
//    }
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateReport(Report report) {
//        if(StringUtils.isNotBlank(report.getRemark())){
//            updateOverdueRemark(report);
//        }
//
//        Report dbReport = this.baseMapper.selectById(report.getId());
//        dbReport.setReportNum(report.getReportNum());
//        int fStatus = report.getStatus();
//
//        if (dbReport.getStatus() == ReportStatus.REJECT.getValue()) {
//            //修改-驳回
//            if (fStatus == 1) {
//                report.setStatus(ReportStatus.REJECT.getValue());
//                //更新填报时间
//                report.setReportTime(new Date());
//                this.baseMapper.updateById(report);
//                updateReportJoin(report);
//            } else {
//                boolean rejectPersonAudit = false; // 返给驳回人审核
//                Integer delFlag = report.getDelFlag();
//                if (delFlag == 3) {
//                    rejectPersonAudit = true;
//                }
//                report.setDelFlag(null);
//                report.setDelFlag(1);
//                Report _report = this.getById(report.getId());
//
//                if (rejectPersonAudit) {
//                    List<Examine> list = examineService.list(new QueryWrapper<Examine>()
//                            .eq("ref_id", _report.getId())
//                            .eq("version", _report.getVersion())
//                            .eq("status", ExamineStatus.REJECT.getValue())
//                            .eq("type", TaskType.OVERSEE.getValue()));
//                    if (CollectionUtils.isNotEmpty(list)) {
//                        if (list.size() > 1) {
//                            logger.warn("驳回人不唯一");
//                        }
//                        Long uid = 0l;
//                        for (Examine examine : list) {
//                            uid = examine.getUserId();
//                            examineService.removeById(examine.getId());
//                            examine.setId(null);
//                            examine.setRemark(null);
//                            examine.setExamineTime(null);
//                            examine.setDelFlag(1);
//                            examine.setStatus(ExamineStatus.UNEXAMINED);
//                            examineService.save(examine);
//                        }
//                        report.setStatus(ReportStatus.UNAUDITED.getValue());
//                        //更新填报时间
//                        report.setReportTime(new Date());
//                        this.baseMapper.updateById(report);
//
//                        //发送检视邮件
//                        pmSendMailService.sendInspectionMail(uid, _report.getId(), ProjectMailType.PM_INSPECTION.getCode(), null);
//
//                        /* 恢复之前的待审核  并重新发送填报邮件 */
//                        Map<String, Object> params = new HashMap<>();
//                        params.put("refId", _report.getId());
//                        params.put("version", _report.getVersion());
//                        params.put("status", ExamineStatus.UNEXAMINED.getValue());
//                        params.put("type", TaskType.OVERSEE.getValue());
//                        params.put("delFlag", BizConstants.DEL_FLAG_YES);
//                        List<Examine> prevUnExamines = examineService.listPrevUnExamine(params);
//                        for (Examine examine : prevUnExamines) {
//                            examine.setDelFlag(BizConstants.DEL_FLAG_NO);
//                            Long row = examineService.updateDelFlag(examine);
//                        }
//                        for (Examine examine : prevUnExamines) {
//                            pmSendMailService.sendInspectionMail(examine.getUserId(), _report.getId(), ProjectMailType.PM_INSPECTION.getCode(), null);
//                        }
//
//                        //更新填报进度
//                        updateReportPercent(dbReport);
//                        updateReportJoin(report);
//                    } else {
//                        throw new RRException("项目没有被驳回");
//                    }
//                } else {
//                    report.setStatus(ProjectStatus.UNAUDITED.getValue());
//                    report.setVersion(_report.getVersion() + 1);
//                    //更新填报时间
//                    report.setReportTime(new Date());
//                    this.baseMapper.updateById(report);
//
//                    Project project = projectDao.selectById(_report.getProjectId());
//
//                    List<ExamineChain> list = examineChainService.list(new QueryWrapper<ExamineChain>()
//                            .eq("project_id", project.getId()).isNull("pid")
//                            .eq("type", TaskType.OVERSEE.getValue()));
//                    if (CollectionUtils.isNotEmpty(list)) {
//                        if (list.size() != 1) {
//                            throw new RRException("初始审核不唯一");
//                        }
//                    } else {
//                        throw new RRException("找不到审核人");
//                    }
//                    examineService.nextExamine(list, report.getVersion(), _report.getId(),report.getTenantId());
//                    //更新填报进度
//                    updateReportPercent(dbReport);
//                    updateReportJoin(report);
//                }
//            }
//        } else {
//            //修改-正常填报修改
//            if (fStatus == 1) {
//                //更新填报时间
//                report.setReportTime(new Date());
//                this.baseMapper.updateById(report);
//                updateReportJoin(report);
//            } else {
//                //更新填报时间
//                report.setReportTime(new Date());
//                report.setVersion(1);
//                this.baseMapper.updateById(report);
//                Project project = projectDao.selectById(report.getProjectId());
//                examineService.insertChainRecord(project, report);
//                //更新填报进度
//                updateReportPercent(dbReport);
//                updateReportJoin(report);
//            }
//        }
//
//        // 更新任务的填报统计信息
//        taskService.updateTaskFillInInfo(report.getTaskId());
//    }
//
//    @Override
//    public int getReportProcessNum(Long taskId) {
//        return reportDao.getReportProcessNum(taskId);
//    }
//
//    @Override
//    public List<Report> getReportNumByProjectId(Long projectId) {
//        return reportDao.getReportNumByProjectId(projectId);
//    }
//
//    public List<Report> getStatusNumByTaskId(Long taskId) {
//        return reportDao.getStatusNumByTaskId(taskId);
//    }
//
//    @Override
//    public void migrateReportData(Map params) {
//        reportDao.migrateReportData(params);
//    }
//
//    @Override
//    public List<Map> recordReportInfo(Map params) {
//        return reportDao.recordReportInfo(params);
//    }
//
//    @Override
//    public List<Report> getAllTaskRations(String projectId) {
//        return this.baseMapper.getAllTaskRations(projectId);
//    }
//
//    /**
//     * 更新填报进度
//     *
//     * @param report
//     */
//    public void updateReportPercent(Report report) {
//        Task _task = taskDao.getById_(report.getTaskId());
//        Task task = new Task();
//        task.setId(_task.getId());
////        task.setPercent(Double.valueOf(report.getReportNum()));
//        if (_task.getOverStatus() != null && _task.getOverStatus() == OverTimeStatus.OVERTIME.getValue()) {
//            //查询任务其他填报周期有逾期的没有，如果没有将任务状态更新为运行中
//            Long count = taskCycleDao.queryOverNum(_task.getId());
//            if (count == 0) {
//                task.setOverStatus(OverTimeStatus.NORMAL.getValue());
//                taskDao.updateById(task);
//                //查看项目存在逾期任务
//                Project project = projectDao.selectById(_task.getProjectId());
//                if (project.getProjectStatus().equals(ProjectStatus.OVERDUE.getValue())) {
//                    List<Task> taskList = taskDao.selectList(new QueryWrapper<Task>().eq("project_id", _task.getProjectId()).eq("over_status", OverTimeStatus.OVERTIME.getValue()));
//                    if (CollectionUtils.isEmpty(taskList)) {
//                        project.setProjectStatus(ProjectStatus.RUNNING.getValue());
//                        projectDao.updateById(project);
//                    }
//                }
//            }
//        }
//        taskDao.updateById(task);
//
//
//    }
//
//
//    public void insertReportJoin(Report report){
//        //保存责任人填报记录
//        ReportJoin reportJoin = new ReportJoin();
//        reportJoin.setTenantId(report.getTenantId());
//        reportJoin.setProjectId(report.getProjectId());
//        reportJoin.setTaskId(report.getTaskId());
//        reportJoin.setReportId(report.getId());
//        reportJoin.setCycleId(report.getReportCycleId());
//        reportJoin.setType(1);
//        reportJoin.setPersonId(report.getCreater());
//        reportJoin.setSpendHrs(report.getSpendHrs());
//        reportJoin.setReportTime(new Date());
//        reportJoin.setDelFlag(1);
//        reportJoin.setCreateUser(report.getCreater());
//        reportJoin.setReportProcess(report.getReportProcess());
//        reportJoinService.save(reportJoin);
//
//        if(CollectionUtils.isNotEmpty(report.getParticipants())&&report.getParticipants().size()>0){
//            //保存参与人填报记录
//            ReportJoin rj;
//            for (Map<String,Object> map : report.getParticipants()) {
//                rj = new ReportJoin();
//                rj.setTenantId(report.getTenantId());
//                rj.setProjectId(report.getProjectId());
//                rj.setTaskId(report.getTaskId());
//                rj.setReportId(report.getId());
//                rj.setCycleId(report.getReportCycleId());
//                rj.setType(2);
//                rj.setPersonId(Long.valueOf(map.get("userId").toString()));
//                rj.setSpendHrs(Integer.valueOf(map.get("spendHrs").toString()));
//                rj.setReportTime(new Date());
//                rj.setDelFlag(1);
//                rj.setCreateUser(report.getCreater());
//                rj.setReportProcess(report.getReportProcess());
//                reportJoinService.save(rj);
//            }
//        }
//    }
//
//    /**
//     * 更新参与人填报记录
//     * @param report
//     */
//    public void updateReportJoin(Report report){
//        //更新参与人填报记录表
//        //1、更新责任人填报记录
//        ReportJoin reportJoin = new ReportJoin();
//        reportJoin.setCycleId(report.getReportCycleId());
//        reportJoin.setSpendHrs(report.getSpendHrs());
//        reportJoin.setReportProcess(report.getReportProcess());
//        reportJoinService.update(reportJoin,new QueryWrapper<ReportJoin>().eq("report_id",report.getId()).eq("type",1));
//        //2、更新参与人填报信息
//        if(CollectionUtils.isNotEmpty(report.getParticipants())&&report.getParticipants().size()>0){
//            ReportJoin rj;
//            for (Map<String,Object> map : report.getParticipants()) {
//                rj = new ReportJoin();
//                rj.setId(Long.valueOf(map.get("id").toString()));
//                rj.setCycleId(report.getReportCycleId());
//                rj.setSpendHrs(Integer.valueOf(map.get("spendHrs").toString()));
//                rj.setReportProcess(report.getReportProcess());
//                reportJoinService.updateById(rj);
//            }
//        }
//    }

    public RptReport getReportByid(String id){
        return reportDao.getReportByid(id);
    }
}

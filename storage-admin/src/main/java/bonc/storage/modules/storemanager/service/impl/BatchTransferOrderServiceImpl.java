package bonc.storage.modules.storemanager.service.impl;

import bonc.storage.modules.storemanagent.dao.RrsStoreBasicInfoMapper;
import bonc.storage.modules.storemanagent.entity.RrsStoreBasicInfo;
import bonc.storage.modules.storemanager.dao.*;
import bonc.storage.modules.storemanager.entity.*;
import bonc.storage.modules.storemanager.service.IBatchTransferOrderService;
import bonc.storage.modules.storemanager.service.IRRSGoodsEnterOrderService;
import bonc.storage.modules.storemanager.vo.TransferImportVO;
import bonc.storage.modules.storeplacemanagent.dao.RrsStorePositionInfoMapper;
import bonc.storage.modules.storeplacemanagent.entity.RrsStorePositionInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class BatchTransferOrderServiceImpl implements IBatchTransferOrderService {

    private final RRSGoodsTransferDao rrsGoodsTransferDao;
    private final RRSGoodsTransferOrderDao rrsGoodsTransferOrderDao;
    private final RRSGoodsLeaveOrderDao rrsGoodsLeaveOrderDao;
    private final RrsStoreBasicInfoMapper storeBasicInfoMapper;
    private final RrsStorePositionInfoMapper storePositionInfoMapper;
    private final MaterialInformationDao materialInformationDao;
    private final RRSStoreStockGoodsDao storeStockGoodsDao;
    private final RRSStoreSnInfoDao storeSnInfoDao;
    private final IRRSGoodsEnterOrderService irrsGoodsEnterOrderService;
    private final Validator validator;
    private final RRSGoodsEnterOrderDao rrsGoodsEnterOrderDao;
    private final RRSGoodsLeaveRelationDao rrsGoodsLeaveRelationDao;
    private final RRSStoreSnReversalRecordDao rrsStoreSnReversalRecordDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult processBatchImport(List<TransferImportVO> importList) {
        log.info("开始处理批量调拨导入，共 {} 条数据", importList.size());

        // 1. 填充数据
        populateImportData(importList);

        // 2. 数据校验
        List<TransferImportVO> failedRows = validateAllRows(importList);
        if (!failedRows.isEmpty()) {
            log.warn("数据校验失败，共 {} 条错误数据", failedRows.size());
            return new ImportResult(0, failedRows);
        }

        log.info("数据校验通过，开始执行数据库操作");

        // 3. 执行导入
        int successCount = 0;
        for (TransferImportVO importData : importList) {
            try {
                // 在循环内部处理每一行，如果失败，整个事务回滚
                processSingleImport(importData);
                successCount++;
            } catch (Exception e) {
                log.error("第 {} 行数据处理失败，事务将回滚。错误：{}", importData.getRowIndex(), e.getMessage(), e);
                // 记录错误信息并抛出异常以回滚事务
                importData.setErrorMessage("处理失败：" + e.getMessage());
                failedRows.add(importData);
                //throw new RuntimeException("数据处理失败，回滚所有操作", e);
            }
        }

        if (!failedRows.isEmpty()) {
            log.warn("数据保存失败，共 {} 条错误数据", failedRows.size());
            //回滚事务
            // 手动标记事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new ImportResult(0, failedRows);
        }

        return new ImportResult(successCount, new ArrayList<>());
    }

    /**
     * 填充导入数据中的ID字段
     * 根据名称查询对应的ID，为后续校验和处理做准备
     */
    private void populateImportData(List<TransferImportVO> importList) {
        log.info("开始填充导入数据");

        // 提取所有需要查询的数据
        List<String> sns = importList.stream().map(TransferImportVO::getSnCode).distinct().collect(Collectors.toList());
        List<String> outStoreNames = importList.stream().map(TransferImportVO::getOutStoreName).distinct().collect(Collectors.toList());
        List<String> inStoreNames = importList.stream().map(TransferImportVO::getInStoreName).distinct().collect(Collectors.toList());
        List<String> inPositionNames = importList.stream().map(TransferImportVO::getInPositionName).distinct().collect(Collectors.toList());

        List<String> storeNames = new ArrayList<>(outStoreNames);
        storeNames.addAll(inStoreNames);

        // 预加载数据
        //sn码信息
        List<RRSStoreSnInfoEntity> snInfos = getAllSns(sns);
        // 入仓库 key 仓库名称
        Map<String, RrsStoreBasicInfo> storeMap = getAllStores(storeNames).stream()
                .collect(Collectors.toMap(RrsStoreBasicInfo::getStoreName, Function.identity(), (a, b) -> a));
        // 入库仓位 key 仓库ID_仓位名称
        Map<String, RrsStorePositionInfo> inPositionMap = getAllPositions(inPositionNames).stream()
                .collect(Collectors.toMap(p -> p.getStoreId() + "_" + p.getPositionName(), Function.identity(), (a, b) -> a));

        // sn key SN编码
        Map<String, RRSStoreSnInfoEntity> snMap = getAllSns(sns).stream()
                .collect(Collectors.toMap(RRSStoreSnInfoEntity::getSn, Function.identity(), (a, b) -> a));

        //物料ID 去重
        List<Integer> materielIds = snInfos.stream().map(RRSStoreSnInfoEntity::getMaterielId).distinct().collect(Collectors.toList());
        Map<Integer, MaterielInformationEntity> materielMap = getAllMateriels(materielIds).stream()
                .collect(Collectors.toMap(MaterielInformationEntity::getId, Function.identity(), (a, b) -> a));

        // 填充每行数据
        for (TransferImportVO importVO : importList) {
            // 填充出库仓库ID
            RrsStoreBasicInfo outStore = storeMap.get(importVO.getOutStoreName());
            if (outStore != null) {
                importVO.setOutStoreId(outStore.getId());
            }

            // 填充入库仓库ID
            RrsStoreBasicInfo inStore = storeMap.get(importVO.getInStoreName());
            if (inStore != null) {
                importVO.setInStoreId(inStore.getId());
            }

            RRSStoreSnInfoEntity sn = snMap.get(importVO.getSnCode());
            if (sn != null) {
                importVO.setSnStatus(sn.getStatus());
                importVO.setMaterielId(sn.getMaterielId());
                // 根据sn码填充出库仓位ID
                importVO.setOutPositionId(sn.getPositionId());

                // 填充sn码信息
                importVO.setSnInfo(sn);
                // 填充物料信息
                importVO.setMaterielInfo(materielMap.get(sn.getMaterielId()));

            }

            // 填充入库仓位ID
            RrsStorePositionInfo inPosition = inPositionMap.get(importVO.getInStoreId() + "_" + importVO.getInPositionName());
            if (inPosition != null) {
                importVO.setInPositionId(inPosition.getId());
            }

        }

        log.info("数据填充完成");
    }

    private List<TransferImportVO> validateAllRows(List<TransferImportVO> importList) {
        List<TransferImportVO> failedRows = new ArrayList<>();

        for (TransferImportVO row : importList) {
            // 1. JSR 303 Validation
            Set<ConstraintViolation<TransferImportVO>> violations = validator.validate(row);
            StringBuilder errors = new StringBuilder();
            if (!violations.isEmpty()) {
                for (ConstraintViolation<TransferImportVO> violation : violations) {
                    errors.append(violation.getMessage()).append("；");
                }
                row.setErrorMessage(errors.toString());
                failedRows.add(row);
            }

        }
        return failedRows;
    }

    private void processSingleImport(TransferImportVO importData) {
        // 预先准备所有需要的实体数据
        BatchTransferData batchData = prepareBatchTransferData(importData);

        // 一次性执行所有数据库操作
        executeBatchTransferOperations(batchData);

        log.info("批量调拨处理完成，调拨单号: {}, 出库单号: {}, 入库单号: {}",
                batchData.transferOrder.getApplyCode(),
                batchData.leaveOrder.getLeaveNum(),
                batchData.enterOrder.getEnterCode());
    }

    /**
     * 准备批量调拨所需的所有数据
     */
    private BatchTransferData prepareBatchTransferData(TransferImportVO importData) {

        if (!importData.getOutStoreId().equals(importData.getSnInfo().getStoreId())) {
            throw new RuntimeException("该sn码当前所在仓库和出库仓库不一致！");
        }

        if (!importData.getMaterielName().equals(importData.getMaterielInfo().getMaterielName())) {
            throw new RuntimeException("该sn码物料名称和导入物料名称不一致！");
        }


        BatchTransferData data = new BatchTransferData();
        Date now = new Date();
        SysUserEntity currentUser = getUser();
        Integer goodsType = importData.getSnInfo().getGoodsType();

        // 1. 准备调拨单数据（状态直接设为审核通过）
        data.transferOrder = new RRSGoodsTransferOrderEntity();
        data.transferOrder.setApplyCode(irrsGoodsEnterOrderService.getMoveCodeAndTime());
        data.transferOrder.setApplyTime(now);
        data.transferOrder.setSrcStoreId(importData.getOutStoreId());
        data.transferOrder.setStoreId(importData.getInStoreId());
        // 直接设为审核通过
        data.transferOrder.setApplyStatus("1");
        // 批量导入模式
        data.transferOrder.setApplyMode("2");
        data.transferOrder.setApplyMemo("批量导入调拨 - " + importData.getMaterielName());
        data.transferOrder.setCreatTime(now);
        data.transferOrder.setUpdateTime(now);
        data.transferOrder.setOperateUser(currentUser.getUsername());
        data.transferOrder.setUserId(String.valueOf(currentUser.getUserId()));
        data.transferOrder.setExpressNum(importData.getExpressNum());
        data.transferOrder.setFromName(importData.getReceiver());
        data.transferOrder.setPhone(importData.getPhone());
        data.transferOrder.setAddress(importData.getProvince() + "," + importData.getCity() + "," + importData.getDetailAddress());

        // 2. 准备调拨物料信息
        data.transferGoods = new RRSTransferGoodsInfoEntity();
        data.transferGoods.setGoodsId(importData.getMaterielId());
        data.transferGoods.setGoodsName(importData.getMaterielName());
        data.transferGoods.setGoodsType(goodsType);
        data.transferGoods.setGoodsUnit(importData.getMaterielInfo().getMaterielUnitValue());
        data.transferGoods.setGoodsNum(importData.getApplyTransferNum());
        // 实际数量等于申请数量
        data.transferGoods.setActualNum(importData.getApplyTransferNum());
        data.transferGoods.setOutPositionId(importData.getOutPositionId());
        data.transferGoods.setInPositionId(importData.getInPositionId());
        data.transferGoods.setCreatTime(now);
        data.transferGoods.setUpdateTime(now);

        // 3. 准备出库单数据（状态直接设为已完成）
        data.leaveOrder = new RRSGoodsLeaveOrderEntity();
        data.leaveOrder.setLeaveNum(irrsGoodsEnterOrderService.getLeaveCodeAndTime());
        data.leaveOrder.setLeaveDate(now);
        data.leaveOrder.setStoreId(importData.getOutStoreId());
        // 调拨出库
        data.leaveOrder.setLeaveType(1);
        // 直接设为已完成
        data.leaveOrder.setGoStatus("1");
        data.leaveOrder.setOperateUser(currentUser.getUsername());
        data.leaveOrder.setLeaveMemo("批量调拨生成出库单");
        data.leaveOrder.setCreatTime(now);
        data.leaveOrder.setUpdateTime(now);
        data.leaveOrder.setNum(importData.getApplyTransferNum());
        data.leaveOrder.setOutCode(data.transferOrder.getApplyCode());
        // 调拨出库场景
        data.leaveOrder.setOutScenario("2");

        // 4. 准备出库明细信息
        data.leaveInfo = new RRSEnterLeaveInfoEntity();
        data.leaveInfo.setEnterCode(data.leaveOrder.getLeaveNum());
        data.leaveInfo.setCreateTime(now);
        data.leaveInfo.setStoreId(importData.getOutStoreId());
        // 出库
        data.leaveInfo.setMode("1");
        // 已完成
        data.leaveInfo.setStatus("1");
        data.leaveInfo.setOutCode(data.transferOrder.getApplyCode());
        // 快递 信息
        data.leaveInfo.setFromName(importData.getReceiver());
        data.leaveInfo.setExpressNum(importData.getExpressNum());
        data.leaveInfo.setPhone(importData.getPhone());
        data.leaveInfo.setAddress(importData.getProvince() + "," + importData.getCity() + "," + importData.getDetailAddress());

        // 4. 准备出库物料关系
        data.leaveRelation = new RRSGoodsLeaveRelationEntity();
        data.leaveRelation.setGoodsId(importData.getMaterielId());
        data.leaveRelation.setGoodsType(goodsType);
        data.leaveRelation.setLeaveNum(importData.getApplyTransferNum());
        data.leaveRelation.setStorePositionId(importData.getOutPositionId());
        //总库存
        RRSStoreStockGoodsEntity goodsEntity = rrsGoodsEnterOrderDao.queryGoodsStock(importData.getMaterielId(), importData.getOutPositionId(), goodsType, importData.getOutStoreId());
        int goodsTotal = goodsEntity != null ? goodsEntity.getGoodsTotal() : 0;
        data.leaveRelation.setExNum(goodsTotal);
        //预计出库数量
        data.leaveRelation.setExpectOutNum(importData.getApplyTransferNum());
        if (goodsTotal < importData.getApplyTransferNum()) {
            throw new RuntimeException("出库数量不能大于总库存数量");
        }
        data.leaveRelation.setMaterielSn(importData.getSnCode());
        data.leaveRelation.setCreatTime(now);

        // 5. 准备入库单数据（状态为预录）
        data.enterOrder = new RRSGoodsEnterOrderEntity();
        data.enterOrder.setEnterCode(irrsGoodsEnterOrderService.getEnterCodeAndTime());
        data.enterOrder.setEnterDate(now);
        data.enterOrder.setStoreId(importData.getInStoreId());
        data.enterOrder.setOperateUser(currentUser.getUsername());
        data.enterOrder.setGoodsMemo("调拨生成入库单");
        data.enterOrder.setCreatTime(now);
        // 预录状态
        data.enterOrder.setFromStatus("0");
        data.enterOrder.setNum(importData.getApplyTransferNum());

        // 6. 准备入库物料关系
        data.enterRelation = new RRSGoodsEnterRelationEntity();
        data.enterRelation.setGoodsId(importData.getMaterielId());
        data.enterRelation.setGoodsType(goodsType);
        data.enterRelation.setLeaveNum(importData.getApplyTransferNum());
        data.enterRelation.setEnterNum(importData.getApplyTransferNum());
        data.enterRelation.setStorePositionId(importData.getInPositionId());
        data.enterRelation.setCreatTime(now);
        data.enterRelation.setSnNumber(importData.getSnCode());

        // 8. 准备入库明细信息
        data.enterInfo = new RRSEnterLeaveInfoEntity();
        data.enterInfo.setEnterCode(data.enterOrder.getEnterCode());
        data.enterInfo.setCreateTime(now);
        data.enterInfo.setStoreId(importData.getInStoreId());
        // 入库
        data.enterInfo.setMode("0");
        // 预录
        data.enterInfo.setStatus("0");
        data.enterInfo.setOutCode(data.leaveOrder.getLeaveNum());
        // 快递 信息
        data.leaveInfo.setFromName(importData.getReceiver());
        data.leaveInfo.setExpressNum(importData.getExpressNum());
        data.leaveInfo.setPhone(importData.getPhone());
        data.leaveInfo.setAddress(importData.getProvince() + "," + importData.getCity() + "," + importData.getDetailAddress());
        data.enterInfo.setIsManufacturerInStorage(importData.getIsManufacturerInStorage());

        // 9. 准备sn号操作记录
        data.snReversalRecord = new RRSStoreSnReversalRecordEntity();
        data.snReversalRecord.setSn(importData.getSnCode());
        data.snReversalRecord.setMaterielId(importData.getMaterielId());
        data.snReversalRecord.setFromStoreId(importData.getOutStoreId());
        data.snReversalRecord.setFromPositionId(importData.getOutPositionId());
        data.snReversalRecord.setBeforeStatus(importData.getSnInfo().getStatus());
        data.snReversalRecord.setBeforeGoodsType(goodsType);
        data.snReversalRecord.setToStoreId(importData.getInStoreId());
        data.snReversalRecord.setToPositionId(importData.getInPositionId());
        data.snReversalRecord.setCurrentStatus(2);
        data.snReversalRecord.setCurrentGoodsType(goodsType);
        data.snReversalRecord.setInsertTime(new Date());
        data.snReversalRecord.setIsDelete(IntegerEnum.ZERO.getValue());
        data.snReversalRecord.setOperator(currentUser.getUsername());

        data.importData = importData;
        return data;
    }

    /**
     * 执行批量调拨的所有数据库操作
     */
    private void executeBatchTransferOperations(BatchTransferData data) {
        // 1. 创建调拨单--审核通过
        rrsGoodsTransferOrderDao.insert(data.transferOrder);
        // 2. 创建调拨单审核记录--审核通过
        rrsGoodsTransferOrderDao.insertAuditRecord2(data.transferOrder.getApplyCode(), data.transferOrder.getOperateUser(), 1, data.transferOrder.getOperateUser(), "");

        // 3. 创建调拨物料信息
        data.transferGoods.setApplyId(data.transferOrder.getId());
        rrsGoodsTransferDao.insert(data.transferGoods);

        // 4. 创建出库单
        rrsGoodsLeaveOrderDao.insert(data.leaveOrder);

        // 5. 创建出库物料关系
        data.leaveRelation.setLeaveId(data.leaveOrder.getId());
        rrsGoodsLeaveRelationDao.insert(data.leaveRelation);

        // 6. 创建入库单
        rrsGoodsEnterOrderDao.insert(data.enterOrder);

        // 7. 创建入库物料关系
        data.enterRelation.setEnterId(data.enterOrder.getId());
        rrsGoodsEnterOrderDao.insertGoodsEnterRelation(data.enterRelation);

        // 8. 创建出库明细
        rrsGoodsEnterOrderDao.insertEnterLeaveInfo(data.leaveInfo);

        // 9. 创建入库明细
        rrsGoodsEnterOrderDao.insertEnterLeaveInfo(data.enterInfo);

        // 10. 更新库存
        // 减少出库仓库库存
        RRSStoreStockGoodsEntity outStock = storeStockGoodsDao.selectOne(
                new QueryWrapper<RRSStoreStockGoodsEntity>()
                        .eq("goods_id", data.importData.getMaterielId())
                        .eq("store_id", data.importData.getOutStoreId())
                        .eq("store_position_id", data.importData.getOutPositionId())
                        .eq("goods_type", data.importData.getSnInfo().getGoodsType())
        );
        if (outStock != null) {
            int goodsNum = outStock.getGoodsTotal() - data.importData.getApplyTransferNum();
            if (goodsNum < 0) {
                throw new RuntimeException("出库数量不能大于总库存数量！");
            }
            outStock.setGoodsTotal(goodsNum);
            outStock.setRealGoodsTotal(goodsNum);
            outStock.setLatestOutTime(new Date());
            storeStockGoodsDao.updateById(outStock);
        }

        // 11. 更新SN码状态
        RRSStoreSnInfoEntity snInfo = storeSnInfoDao.selectOne(
                new QueryWrapper<RRSStoreSnInfoEntity>()
                        .eq("sn", data.importData.getSnCode())
                        .eq("materiel_id", data.importData.getMaterielId())
                        .eq("store_id", data.importData.getOutStoreId())
        );
        if (snInfo != null) {
            if (snInfo.getStatus() != 1 && snInfo.getStatus() != 7) {
                throw new RuntimeException("SN码状态不为在库状态！");
            }
            // 更新SN码状态 调拨出库
            snInfo.setStatus(2);
            snInfo.setUpdateTime(new Date());
            storeSnInfoDao.updateById(snInfo);
            rrsStoreSnReversalRecordDao.insert(data.snReversalRecord);
        }
    }

    /**
     * 批量调拨数据容器
     */
    public static class BatchTransferData {
        public RRSStoreSnReversalRecordEntity snReversalRecord;
        RRSGoodsTransferOrderEntity transferOrder;
        RRSTransferGoodsInfoEntity transferGoods;
        RRSGoodsLeaveOrderEntity leaveOrder;
        RRSGoodsLeaveRelationEntity leaveRelation;
        RRSGoodsEnterOrderEntity enterOrder;
        RRSGoodsEnterRelationEntity enterRelation;
        RRSEnterLeaveInfoEntity leaveInfo;
        RRSEnterLeaveInfoEntity enterInfo;
        TransferImportVO importData;
    }

    public SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }

    // 辅助方法，用于预加载数据
    private List<RrsStoreBasicInfo> getAllStores(List<String> storeNames) {
        return storeBasicInfoMapper.selectList(new QueryWrapper<RrsStoreBasicInfo>().in("store_name", storeNames));
    }

    private List<RrsStorePositionInfo> getAllPositions(List<String> positionNames) {
        return storePositionInfoMapper.selectList(new QueryWrapper<RrsStorePositionInfo>().in("position_name", positionNames));
    }

    private List<MaterielInformationEntity> getAllMateriels(List<Integer> materielIds) {
        return materialInformationDao.selectList(new QueryWrapper<MaterielInformationEntity>().in("id", materielIds));
    }

    private List<RRSStoreSnInfoEntity> getAllSns(List<String> sns) {
        return storeSnInfoDao.selectList(new QueryWrapper<RRSStoreSnInfoEntity>().in("sn", sns));
    }
}

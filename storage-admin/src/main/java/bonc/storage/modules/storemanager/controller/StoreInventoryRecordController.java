package bonc.storage.modules.storemanager.controller;


import bonc.storage.modules.storemanager.service.StoreInventoryRecordService;
import bonc.storage.modules.storemanager.vo.StartInventoryVo;
import bonc.storage.modules.storemanager.vo.StoreInventoryRecordVo;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 盘库记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-19
 */
@RestController
@RequestMapping("/storeInventoryRecord")
@RequiredArgsConstructor
@Api(tags = "盘库记录表前端控制器")
public class StoreInventoryRecordController {

    final StoreInventoryRecordService storeInventoryRecordService;

    /**
     * 展示盘库列表
     *
     * @param params
     * @return
     */
    @PostMapping("/list")
    @ApiOperation("盘库列表展示接口")
    public R list(@Validated @RequestBody StoreInventoryRecordVo params) {
        return storeInventoryRecordService.queryList(params);
    }

    /**
     * 开始盘点接口
     *
     * @param params
     * @return
     */
    @PostMapping("/startInventory")
    @ApiOperation("开始盘点接口")
    public R startInventory(@RequestBody StartInventoryVo params) {
        return storeInventoryRecordService.startInventory(params);
    }

    @GetMapping("/getInventoryFile")
    @ApiOperation("获取盘库单资料")
    public R getInventoryFile(Integer id) {
        return R.ok().put("imgPath", storeInventoryRecordService.getInventoryFile(id));
    }


    @PostMapping("/updateInventoryFile")
    @ApiOperation("更新盘库单见证性资料")
    public R updateInventoryFile(@RequestBody StartInventoryVo params) {
        return storeInventoryRecordService.updateInventoryFile(params);
    }

    /**
     * 作废盘库记录接口
     * @param id
     * @return
     */
    @PostMapping("/deleteRecord")
    @ApiOperation("作废盘库记录接口")
    public R deleteRecord(@RequestBody String id){
        return storeInventoryRecordService.deleteRecord(id);
    }
}


/**
 * youngking 感谢开源社区的贡献，请记住吃水不忘挖井人！！
 */

package com.youngking.renrenwithactiviti.modules.job.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.youngking.lenmoncore.common.utils.Constant;
import com.youngking.lenmoncore.common.utils.PageUtils;
import com.youngking.lenmoncore.common.utils.Query;
import com.youngking.renrenwithactiviti.modules.job.dao.ScheduleJobDao;
import com.youngking.renrenwithactiviti.modules.job.entity.ScheduleJobEntity;
import com.youngking.renrenwithactiviti.modules.job.service.ScheduleJobService;
import com.youngking.renrenwithactiviti.modules.job.utils.ScheduleUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.quartz.CronTrigger;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

@Service("scheduleJobService")
@Slf4j
public class ScheduleJobServiceImpl extends ServiceImpl<ScheduleJobDao, ScheduleJobEntity> implements ScheduleJobService {
    @Autowired
    private Scheduler scheduler;
    @Value("${ip.address}")
    private String ipAddress;
    @Value("${scheduler.group}")
    private String scheduleGroup;

    private static String SCHEDULE_GROUP_STORAGE = "rrs_storage";
    private static String SCHEDULE_GROUP_BACK = "rrs_back";


    /**
     * 项目启动时，初始化定时器
     */
    @PostConstruct
    public void init() {
        InetAddress localhost = null;
        try {
            localhost = Inet4Address.getLocalHost();
            log.debug("localhost: " +localhost);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        String ip = localhost.getHostAddress();
        log.debug("ip: " +ip);
        log.debug("ipAddress: " +ipAddress);
        //String[] ips = ipAddress.split(",");
        List<String> ips = Arrays.asList(ipAddress.split(","));
        log.debug(ips.toString());
        log.debug("scheduleGroup:" + scheduleGroup);
        if (StringUtils.isNotBlank(ip) && ips.contains(ip) && !StringUtils.equals(scheduleGroup, SCHEDULE_GROUP_STORAGE)) {
            log.debug("启动定时器");
            List<ScheduleJobEntity> scheduleJobList = this.list(new QueryWrapper<ScheduleJobEntity>().eq("schedule_group", scheduleGroup));
            for (ScheduleJobEntity scheduleJob : scheduleJobList) {
                CronTrigger cronTrigger = ScheduleUtils.getCronTrigger(scheduler, scheduleJob.getJobId(), scheduleGroup);
                //如果不存在，则创建
                if (cronTrigger == null) {
                    ScheduleUtils.createScheduleJob(scheduler, scheduleJob, scheduleGroup);
                } else {
                    ScheduleUtils.updateScheduleJob(scheduler, scheduleJob, scheduleGroup);
                }
            }
        }
    }

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String beanName = (String) params.get("beanName");

        IPage<ScheduleJobEntity> page = this.page(
                new Query<ScheduleJobEntity>().getPage(params),
                new QueryWrapper<ScheduleJobEntity>().like(StringUtils.isNotBlank(beanName), "bean_name", beanName)
        );

        return new PageUtils(page);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveJob(ScheduleJobEntity scheduleJob) {
        scheduleJob.setCreateTime(new Date());
        scheduleJob.setStatus(Constant.ScheduleStatus.NORMAL.getValue());
        scheduleJob.setScheduleGroup(scheduleGroup);
        this.save(scheduleJob);

        ScheduleUtils.createScheduleJob(scheduler, scheduleJob, scheduleGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ScheduleJobEntity scheduleJob) {
        ScheduleUtils.updateScheduleJob(scheduler, scheduleJob, scheduleGroup);

        this.updateById(scheduleJob);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(Long[] jobIds) {
        for (Long jobId : jobIds) {
            ScheduleUtils.deleteScheduleJob(scheduler, jobId, scheduleGroup);
        }

        //删除数据
        this.removeByIds(Arrays.asList(jobIds));
    }

    @Override
    public int updateBatch(Long[] jobIds, int status) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("list", jobIds);
        map.put("status", status);
        return baseMapper.updateBatch(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(Long[] jobIds) {
        for (Long jobId : jobIds) {
            ScheduleUtils.run(scheduler, this.getById(jobId), scheduleGroup);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pause(Long[] jobIds) {
        for (Long jobId : jobIds) {
            ScheduleUtils.pauseJob(scheduler, jobId, scheduleGroup);
        }

        updateBatch(jobIds, Constant.ScheduleStatus.PAUSE.getValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resume(Long[] jobIds) {
        for (Long jobId : jobIds) {
            ScheduleUtils.resumeJob(scheduler, jobId, scheduleGroup);
        }

        updateBatch(jobIds, Constant.ScheduleStatus.NORMAL.getValue());
    }

    public static void main(String[] args) {
        InetAddress localhost = null;
        try {
            localhost = Inet4Address.getLocalHost();
            log.debug("localhost: " +localhost);
        } catch (UnknownHostException e) {
            e.printStackTrace();
        }
        String ip = localhost.getHostAddress();
        log.debug("ip: " +ip);
    }

}

package com.bonc.rrs.worder.dto.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class CandidateBranchVo {
	@ApiModelProperty(value = "指派服务兵ID")
	private Integer candidate;
	@ApiModelProperty(value = "工单号")
	private String worderNo;
	@ApiModelProperty(value = "事件")
	private String event;
	@ApiModelProperty(value = "网点ID")
	private Integer branchId;
	@ApiModelProperty(value = "时间")
	private Date time;
}

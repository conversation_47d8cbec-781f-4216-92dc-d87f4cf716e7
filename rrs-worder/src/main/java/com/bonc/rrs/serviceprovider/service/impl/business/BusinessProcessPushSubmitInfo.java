package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushSubmitInfo;
import com.bonc.rrs.byd.domain.PushSubmitReviewInfo;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.xk.service.XkApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;


@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushSubmitInfo extends AbstractBusinessProcess{
    final XkApiService xkApiService;

    @Override
    public String getProcessCode() {
        return "pushSubmitInfo";
    }

    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        try {
            PushSubmitInfo pushInstallationInfo = businessProcessPo.getPushSubmitInfo();
            //服务商提交审核
            PushApiResponse pushApiResponse = xkApiService.pushSubmitInfo(pushInstallationInfo);
            return Result.pushApiResponse(pushApiResponse);
        }catch (Exception e){
            log.error("服务商提交审核回传出现异常", e);
            return Result.error("服务商提交审核回传失败");
        }

    }
}

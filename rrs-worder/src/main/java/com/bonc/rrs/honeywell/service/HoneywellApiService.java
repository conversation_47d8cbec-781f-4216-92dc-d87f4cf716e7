package com.bonc.rrs.honeywell.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.lock.annotation.Lock4j;
import com.bonc.rrs.honeywell.config.HoneywellConfig;
import com.bonc.rrs.honeywell.entity.*;
import com.bonc.rrs.intf.service.IntfLogService;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.RedisUtils;
import com.youngking.lenmoncore.common.utils.Signature.SignatureGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriTemplate;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class HoneywellApiService {

    private final RestTemplate restTemplate;
    private final HoneywellConfig honeywellConfig;
    private final RedisUtils redisUtils;
    private final IntfLogService intfLogService;

    public static final String TOKEN_REDIS_KEY = "honeywell:token";

    public String getToken() {
        String token = redisUtils.get(TOKEN_REDIS_KEY);

        if (token != null) {
            // Redis 中存在 token，直接返回
            return token;
        } else {
            // 如果 Redis 中没有 token，则尝试获取新的 token
            return refreshToken();
        }
    }

    public void updateDispatch(UpdateDispatchRequest request) {
        // @formatter:off
        ParameterizedTypeReference<HoneywellResponse<Boolean>> responseType = new ParameterizedTypeReference<HoneywellResponse<Boolean>>() {};
        Map<String, String> parsedObject = JSON.parseObject(JSON.toJSONString(request), new TypeReference<Map<String, String>>() {});
        // @formatter:on

        request.setSignature(SignatureGenerator.generateSignatureMD5(honeywellConfig.getClientSecret(), parsedObject));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(getToken());

        HttpEntity<UpdateDispatchRequest> entity = new HttpEntity<>(request, headers);
        execute(request.getOrderCode(), HoneywellApiEnum.UPDATE_DISPATCH, HttpMethod.POST, entity, responseType);
    }

    public void finishDispatch(FinishDispatchRequest request) {
        // @formatter:off
        ParameterizedTypeReference<HoneywellResponse<Boolean>> responseType = new ParameterizedTypeReference<HoneywellResponse<Boolean>>() {};
        Map<String, String> parsedObject = JSON.parseObject(JSON.toJSONString(request), new TypeReference<Map<String, String>>() {});
        // @formatter:on
        request.setSignature(SignatureGenerator.generateSignatureMD5(honeywellConfig.getClientSecret(), parsedObject));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(getToken());

        HttpEntity<FinishDispatchRequest> entity = new HttpEntity<>(request, headers);
        execute(request.getOrderCode(), HoneywellApiEnum.FINISH_DISPATCH, HttpMethod.POST, entity, responseType);
    }

    @Lock4j
    private String refreshToken() {
        // 再次检查 Redis，以防其他线程已获取并更新了 token
        String token = redisUtils.get(TOKEN_REDIS_KEY);
        if (token != null) {
            return token;
        }

        // @formatter:off
        ParameterizedTypeReference<HoneywellResponse<TokenResponse>> responseType = new ParameterizedTypeReference<HoneywellResponse<TokenResponse>>() {};
        // @formatter:on
        String url = honeywellConfig.getBaseUrl() + HoneywellApiEnum.GET_TOKEN.getUri();
        HashMap<String, String> variableMap = new HashMap<>();
        variableMap.put("clientId", honeywellConfig.getClientId());
        variableMap.put("clientSecret", honeywellConfig.getClientSecret());
        UriTemplate uriTemplate = new UriTemplate(url);
        URI expandedUrl = uriTemplate.expand(variableMap);
        ResponseEntity<HoneywellResponse<TokenResponse>> responseEntity = restTemplate.exchange(expandedUrl, HttpMethod.GET, null, responseType);
        checkResponse(responseEntity);
        HoneywellResponse<TokenResponse> body = responseEntity.getBody();
        if (body == null || body.getData() == null) {
            log.error("请求霍尼韦尔失败, url:{}, response:{}", expandedUrl, responseEntity);
            throw new RRException("请求霍尼韦尔失败");
        }
        TokenResponse newToken = body.getData();
        log.info("getToken success, expires in {}", newToken.getExpiresIn());
        redisUtils.set(TOKEN_REDIS_KEY, newToken.getAccessToken(), newToken.getExpiresIn());
        return newToken.getAccessToken();
    }

    public <T> T execute(String orderCode, HoneywellApiEnum honeywellApiEnum, HttpMethod method, HttpEntity<?> entity,
                         ParameterizedTypeReference<T> responseType, Object... uriVariables) {
        ResponseEntity<T> responseEntity;
        try {
            String url = honeywellConfig.getBaseUrl() + honeywellApiEnum.getUri();
            responseEntity = restTemplate.exchange(url, method, entity, responseType, uriVariables);
        } catch (Exception e) {
            log.error("请求霍尼韦尔失败:", e);
            throw new RRException("请求霍尼韦尔失败");
        }
        intfLogService.saveIntfLog(honeywellApiEnum.getUri(), orderCode, honeywellApiEnum.getUri(),
                honeywellApiEnum.getDesc(), 5, JSON.toJSONString(entity), JSON.toJSONString(responseEntity));
        checkResponse(responseEntity);
        return responseEntity.getBody();
    }

    private static <T> void checkResponse(ResponseEntity<T> responseEntity) {
        if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
            log.error("请求霍尼韦尔失败:{}", responseEntity);
            throw new RRException("请求霍尼韦尔失败" + responseEntity.getStatusCode() + " " + responseEntity.getStatusCode().getReasonPhrase());
        }
    }

}
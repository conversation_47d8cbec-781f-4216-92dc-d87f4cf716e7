package com.bonc.rrs.gace.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工单检查项表
 * @TableName worder_check_item
 */
@TableName(value ="worder_check_item")
@Data
public class WorderCheckItem implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 工单检查项ID
     */
    @TableId(value = "ck_id")
    private Long ckId;

    /**
     * 工单ID
     */
    @TableField(value = "worder_id")
    private Long worderId;

    /**
     * 序号
     */
    @TableField(value = "ck_number")
    private String ckNumber;

    /**
     * 名称
     */
    @TableField(value = "ck_name")
    private String ckName;

    /**
     * 实际值
     */
    @TableField(value = "ck_actual_value")
    private String ckActualValue;

    /**
     * 描述_详情
     */
    @TableField(value = "ck_actual_description_tag")
    private String ckActualDescriptionTag;

    /**
     * 值类型 (Y/N:是/否, NUM:数字, TEXT:文本说明, CLASS:索引项, METER:仪表, SINGLE:单选, MUTI:多选, DATE:日期)
     */
    @TableField(value = "digi_value_type")
    private String digiValueType;

    /**
     * 检查项组ID
     */
    @TableField(value = "ck_wo_checklist_group_id")
    private Long ckWoChecklistGroupId;

    /**
     * 工单检查项组名称, 示例: "是否具备安装条件"
     */
    @TableField(value = "ckg_name")
    private String ckgName;

    /**
     * 工单检查项组业务场景, 示例: "WOOP_INPRG", 业务场景包括: WO_START:开工前确认, WO_FINISH:完工情况确认, WOOP_WSCH:待计划安排, WOOP_START:任务开工检查, WOOP_INPRG:任务执行, WOOP_FINISH:任务完工情况确认
     */
    @TableField(value = "ckg_scenario")
    private String ckgScenario;

    /**
     * 工单检查项组检查项来源, 示例: "MANUAL", 来源包括: MANUAL:手动创建, ACT:标准作业, WO_TYPE:工单类型
     */
    @TableField(value = "digi_clgroup_source")
    private String digiClgroupSource;

    /**
     * 参考标准
     */
    @TableField(value = "ck_standard_reference")
    private String ckStandardReference;

    /**
     * 辅助录入方式 (SCAN:扫码, LOC:定位)
     */
    @TableField(value = "digi_assist_input")
    private String digiAssistInput;

    /**
     * 多个结果值 (0: false, 1: true)
     */
    @TableField(value = "digi_multiselect")
    private Integer digiMultiselect;

    /**
     * 必检 (0: false, 1: true)
     */
    @TableField(value = "ck_must_check_flag")
    private Integer ckMustCheckFlag;

    /**
     * 描述
     */
    @TableField(value = "ck_actual_description")
    private String ckActualDescription;

    /**
     * 来源标准检查项ID
     */
    @TableField(value = "ck_source_stdchecklist_id")
    private Long ckSourceStdchecklistId;

    /**
     * 上限值
     */
    @TableField(value = "ck_max_range_value")
    private BigDecimal ckMaxRangeValue;

    /**
     * 附件必填 (0: false, 1: true)
     */
    @TableField(value = "digi_annex")
    private Integer digiAnnex;
}
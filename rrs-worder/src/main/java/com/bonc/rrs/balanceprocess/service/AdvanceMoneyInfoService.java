package com.bonc.rrs.balanceprocess.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bonc.rrs.balanceprocess.entity.AdvanceMoneyInfoEntity;

import java.math.BigDecimal;

/**
 * 发布文件表
 *
 * <AUTHOR>
 * @date 2020-07-03 17:47:19
 */
public interface AdvanceMoneyInfoService extends IService<AdvanceMoneyInfoEntity> {

    /**
     * 新资金池更新-预占
     * @param preemptMoney 预占金额
     */
    void preemptMoney(BigDecimal preemptMoney);

    /**
     * 新资金池更新-新资金
     * @param publishMoney 新资金金额
     */
    void publishMoney(BigDecimal publishMoney);

    /**
     * 新资金池更新-平账
     * @param balanceMoney 平账金额
     */
    void balanceMoney(BigDecimal balanceMoney);

    /**
     * 新资金池更新-预占撤销
     * @param backPreemptMoney 回退预占金额
     */
    void backPreemptMoney(BigDecimal backPreemptMoney);

    /**
     * 获取新资金池
     * @return 新资金池信息
     */
    AdvanceMoneyInfoEntity getAdvanceMoneyInfo();
}


package com.bonc.rrs.worderapp.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by zhangyibo on 2020-03-18 16:02
 * 增项物料费用清单
 */

@Data
public class MaterielPriceDto implements Serializable {

    // 工单ID
    private String worderId;
    // 物料ID
    private String materielId;
    // 物料名称
    private String materielName;
    //物料数量
    private BigDecimal num;
    // 实际价格
    private BigDecimal price;
    //物料实际使用数量
    private BigDecimal realNum;
}

package com.bonc.rrs.workManager.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.workManager.entity.RrsScoreSummaryDetailEntity;
import com.bonc.rrs.workManager.entity.vo.DotBrandScoreExport;
import com.bonc.rrs.workManager.entity.vo.DotScoreQueryVo;
import com.bonc.rrs.workManager.entity.vo.ScoreVo;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Description:
 * @Author: liujunpeng
 * @Date: 2023/9/8 14:49
 * @Version: 1.0
 */
@Mapper
public interface RrsScoreSummaryDetailMapper extends BaseMapper<RrsScoreSummaryDetailEntity> {
    List<ScoreVo> queryScoreReal(@Param("param") DotScoreQueryVo param,@Param("userId") Long userId,@Param("flags") Integer flags);

    List<DotBrandScoreExport> queryScoreExport(@Param("param") DotScoreQueryVo param,@Param("userId") Long userId);
}

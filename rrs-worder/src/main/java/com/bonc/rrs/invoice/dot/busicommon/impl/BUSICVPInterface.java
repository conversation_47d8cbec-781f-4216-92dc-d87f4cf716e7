package com.bonc.rrs.invoice.dot.busicommon.impl;

import com.bonc.rrs.invoice.dot.busicommon.BusiCommonInterface;
import com.bonc.rrs.wsdlproperties.WsdlProperties;

import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * This class was generated by Apache CXF 2.7.3
 * 2020-03-18T15:06:39.391+08:00
 * Generated source version: 2.7.3
 * 
 */
/*@WebServiceClient(name = "BUSI_CVP_Interface",
                  wsdlLocation = "http://cvp.hoptest.haier.net/services/busiCommon/Busi_Common_Srv?wsdl",
                  targetNamespace = "http://impl.busiCommon.interfaces.cvp.haier.com/")*/
@WebServiceClient(name = "BUSI_CVP_Interface",
                  wsdlLocation = "${busicvp}",
                  targetNamespace = "http://impl.busiCommon.interfaces.cvp.haier.com/")
public class BUSICVPInterface extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://impl.busiCommon.interfaces.cvp.haier.com/", "BUSI_CVP_Interface");
    public final static QName BusiCommonInterfaceImplPort = new QName("http://impl.busiCommon.interfaces.cvp.haier.com/", "BusiCommonInterfaceImplPort");
    static {
        URL url = null;
        try {
            //url = new URL("http://cvp.hoptest.haier.net/services/busiCommon/Busi_Common_Srv?wsdl");
            url = new URL(WsdlProperties.busicvpurl);
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(BUSICVPInterface.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "http://cvp.hoptest.haier.net/services/busiCommon/Busi_Common_Srv?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public BUSICVPInterface(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public BUSICVPInterface(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public BUSICVPInterface() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    public BUSICVPInterface(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    public BUSICVPInterface(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    public BUSICVPInterface(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     *
     * @return
     *     returns BusiCommonInterface
     */
    @WebEndpoint(name = "BusiCommonInterfaceImplPort")
    public BusiCommonInterface getBusiCommonInterfaceImplPort() {
        return super.getPort(BusiCommonInterfaceImplPort, BusiCommonInterface.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns BusiCommonInterface
     */
    @WebEndpoint(name = "BusiCommonInterfaceImplPort")
    public BusiCommonInterface getBusiCommonInterfaceImplPort(WebServiceFeature... features) {
        return super.getPort(BusiCommonInterfaceImplPort, BusiCommonInterface.class, features);
    }

}

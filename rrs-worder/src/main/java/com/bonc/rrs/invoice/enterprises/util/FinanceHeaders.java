package com.bonc.rrs.invoice.enterprises.util;

import cn.hutool.crypto.SecureUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/1/31 15:30
 */
@Data
public class FinanceHeaders {

    /**
     *
     */
    private String appId;

    private Long dateTime;

    private String sign;

    private FinanceHeaders () {}
    public FinanceHeaders (String appId, String key) {
        this.appId = appId;
        this.dateTime = System.currentTimeMillis();
        this.sign = makeSign(key);
    }

    private String makeSign (String key) {
        return SecureUtil.md5("appId=" + this.appId + "&dateTime=" + this.dateTime + "&key=" + key).toUpperCase();
    }
}

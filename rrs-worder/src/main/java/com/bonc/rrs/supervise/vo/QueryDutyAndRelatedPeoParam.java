package com.bonc.rrs.supervise.vo;

import io.swagger.models.auth.In;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 责任人和相关责任人查询接口入参
 * @Author: liujunpeng
 * @Date: 2023/9/27 10:45
 * @Version: 1.0
 */
@Data
public class QueryDutyAndRelatedPeoParam implements Serializable {

    private Integer worderSystem;

    private Integer worderId;

    private String worderNo;

    @NotNull(message = "缺少督办等级")
    private Integer superviseLv;

    @NotNull(message = "缺少品牌")
    private Integer brandId;

    @NotNull(message = "缺少区域")
    private List<String> areaIds;

    @NotNull(message = "缺少服务属性")
    private Integer serviceType;

    private String typeAbbre;

    private Integer superviseType;
}

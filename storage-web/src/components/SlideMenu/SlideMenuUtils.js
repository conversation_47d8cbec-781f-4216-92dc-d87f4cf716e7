import pathToRegexp from 'path-to-regexp'

//获取menu keys 
export const getMenuKeys = menuData => {
    let keys = []
    menuData.map(item => {
        keys.push(item.path)
        if(item.routes)
            keys = keys.concat(getMenuKeys(item.routes))
    })
    return keys
}

//校验key与path
export const checkKeytToPath = (menuKeys, pathname) => {
    return menuKeys.filter(key => {
        if(key) return pathToRegexp(key).test(pathname)
        return false
    })
} 

//将url转为数组字符串
export const urlToList = pathname => {
    const urlList = pathname.split('/').filter(item => item)
    return urlList.map((item, index) => `/${urlList.slice(0, index + 1).join('/')}`)
}

//获取默认的openkeys
export const getDefaultOpenKeys = (menuKeys, pathname) => {
    return urlToList(pathname)
        .map(key => checkKeytToPath(menuKeys, key)[0])
        .filter(item => item)
        .reduce((prev, cur) => [...prev, cur], ['/'])
}
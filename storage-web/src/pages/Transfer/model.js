import Service from './service'
import { message } from 'antd';
export default {
  namespace: 'transfer',
  state: {
    list: [],
    totalCount: 0,
    visible: false,
    info: null,
    storeList: []
  },
  effects: {
    *list ({ payload: data }, { call, put }) {
      const response = yield call(Service.list, data)
      if (response.code === 0) {
        yield put({ type: 'setList', payload: { list: response.page.list, totalCount: response.totalCount } })
      } else {
        message.error('查询调拨列表失败');
      }
    },
    *storeList({ payload: data }, { call, put }) {
      const response = yield call(Service.storeList, data)
      if (response.code === 0) {
        yield put({ type: 'setStoreList', payload: {
            storeList: response.storeList,
        } })
      }
    }
  },
  reducers: {
    setVisible (state, { visible }) {
      return { ...state, visible }
    },

    setAcInfo (state, { payload: { info } }) {
      return { ...state, info }
    },

    setList (state, { payload: { list, totalCount } }) {
      if (list.length) {
        list.map((item, index) => item.key = index)
      }
      return { ...state, list, totalCount }
    },

    setStoreList(state, { payload: { storeList } }) {
      if (storeList.length) {
        storeList.map((item, index) => item.key = index)
      }
      return { ...state, storeList }
    },
    setCheckStatus(state, { payload: checkStatus}) {
      return { ...state, checkStatus }
    },

  }
}

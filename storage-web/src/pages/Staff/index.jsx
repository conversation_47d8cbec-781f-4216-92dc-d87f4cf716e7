import React, { useEffect, useState } from 'react'
import { View } from 'bonc-element'
import { connect } from 'dva'
import { Form, Input, Button, Table, Modal, Message, Divider, Select } from 'antd'
import StaffWin from './win'
import Service from './service'

const FormItem = Form.Item
const confirm = Modal.confirm;
const StaffPage = ({ form, list, totalCount, dispatch, visible, loading, department }) => {
  const { getFieldDecorator } = form
  const pageLimit = 10;
  const [currentPage, setPage] = useState(1)
  const [search, setSearch] = useState({ con: { name: '', order: 'asc', gender: '', departmentId: '' } })
  const [winType, setType] = useState('save')
  const [selectedRowKeys, setSelectedRowKeys] = useState({ ids: [] })
  const [currStaff, setStaff] = useState(null)
  const handleKin = [{ name: '删除', method: 'deleteStaff' }]
  //组件挂载
  useEffect(() => {
    getDepart();
    getList(1, search.con);
  }, [])
  // 获取部门
  const getDepart = () => {
    if (department.length) return
    dispatch({
      type: 'staff/getDepartment'
    })
  }
  // 获取列表
  const getList = (page, condition) => {
    let params = { page: page, limit: pageLimit, ...condition }
    dispatch({
      type: 'staff/getList',
      params
    })
  }
  const onChange = (page) => {
    setPage(page)
    getList(page, search.con)
    setSelectedRowKeys({ ids: [] })
  }
  // 搜索
  const handleSearch = (e) => {
    e.preventDefault()
    form.validateFields((err, params) => {
      params = { page: 1, limit: pageLimit, order: 'asc', ...params }//sidx:排序字段
      setPage(1)
      setSearch({ con: { ...search.con, ...params } })
      getList(1, params)
      setSelectedRowKeys({ ids: [] })
    })
  }
  //打开
  const handleWinToggle = (type, data) => {
    if (type) {
      setType(type)
    }
    if (data) {
      setStaff(data)
    }
    dispatch({
      type: "staff/setVisible",
      visible: !visible
    })
  }
  // 批量操作
  const handleAll = (idx) => {
    handleStaff(selectedRowKeys.ids, idx)
  }
  const handleStaff = (data, idx) => {
    confirm({
      title: `确定要${handleKin[idx].name}所选员工吗？`,
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      async onOk () {
        const res = await dispatch({
          type: 'staff/handle',
          payload: { method: handleKin[idx].method, data }
        })
        if (res.code === 0) {
          Message.info('成功!', 1).then(() => {
            onChange(1)
          })
        }
      },
      onCancel () {
      },
    });
  }
  const staffDepart = (depart) => {
    let result = department.find(item => item.detailNumber == depart);
    if (result) return result.detailName;
    return '未知';
  }
  const genderObj = { '0': '男', '1': '女' }
  const columns = [{
    title: '员工编号',
    dataIndex: 'id'
  }, {
    title: '员工',
    dataIndex: 'name'
  }, {
    title: '性别',
    dataIndex: 'gender',
    render: (text) => (
      <span>{genderObj[text]}</span>
    )
  }, {
    title: '联系方式',
    dataIndex: 'contact'
  }, {
    title: '所属部门',
    dataIndex: 'departmentId',
    render: (text) => (
      <span>{staffDepart(text)}</span>
    )
  }, {
    title: '操作', render: (text, record) => (
      <span>
        <span className="handle" onClick={handleWinToggle.bind(this, 'update', record)}>修改</span>
        <Divider type="vertical" />
        <span className="handle" onClick={handleStaff.bind(this, [record.id], 0)}>删除</span>
      </span>
    ),
  }];
  // rowSelection object indicates the need for row selection

  const resetForm = () => {
    form.resetFields();
  }
  const rowSelection = {
    onChange: (selectedRowKeys) => {
      setSelectedRowKeys({ ids: selectedRowKeys });
    }
  };
  return <View column>
    <Form layout='inline'>
      <FormItem label='员工'>
        {getFieldDecorator('name', {
          initialValue: ''
        })(
          <Input className="con-width" placeholder='请输入员工' />
        )}
      </FormItem>
      <FormItem label='性别'>
        {getFieldDecorator('gender', {
          initialValue: ''
        })(
          <Select className="con-width" placeholder='请选择性别'>
            <Select.Option value={''}>全部</Select.Option>
            <Select.Option value={'0'}>男</Select.Option>
            <Select.Option value={'1'}>女</Select.Option>
          </Select>
        )}
      </FormItem>
      <FormItem label='所属部门'>
        {getFieldDecorator('departmentId', {
          initialValue: ''
        })(
          <Select className="con-width" placeholder='请选择所属部门'>
            <Select.Option value={''}>全部</Select.Option>
            {department.length ? department.map(item => {
              return <Select.Option value={item.detailNumber} key={item.id}>{item.detailName}</Select.Option>
            }) : ''}
          </Select>
        )}
      </FormItem>
      <FormItem>
        <Button icon='search' type='primary' disabled={loading} onClick={handleSearch}>搜索</Button>
        <Button icon='reload' type='primary' disabled={loading} ghost style={{ margin: '0 5px' }} onClick={resetForm}>重置</Button>
        <Button icon='upload' type='primary disabled={loading}' ghost style={{ margin: '0 5px' }}>导入</Button>
        <Button icon='download' type='primary' disabled={loading} ghost style={{ margin: '0 5px' }}>导出</Button>
        <Button type="primary" ghost disabled={selectedRowKeys.ids.length === 0 || loading} onClick={handleAll.bind(this, 0)} style={{ margin: '0 5px' }}>批量删除</Button>
        <Button icon='plus' type='primary' disabled={loading} ghost onClick={handleWinToggle.bind(this, 'save', null)} >新建</Button>
        {/*<Button icon='close' type='danger' ghost style={{margin: '0 5px'}}*/}
        {/*onClick={confirmDelete.bind(this, selectedRowKeys.ids)}  disabled={loading}>删除</Button>*/}
      </FormItem>
    </Form>
    <View column style={{ marginTop: 10 }}>
      <Table columns={columns} dataSource={list} rowSelection={rowSelection} loading={loading}
        pagination={{ current: currentPage, total: totalCount, onChange: onChange }} />
    </View>
    {visible ? (<StaffWin winType={winType} currStaff={currStaff} reload={onChange} close={handleWinToggle} />) : ''}
  </View>
}
export default connect(({ staff, loading }) => ({
  list: staff.list,
  totalCount: staff.totalCount,
  visible: staff.visible,
  department: staff.department,
  loading: loading.effects['staff/getDepartment'] || loading.effects['staff/getList'] || loading.effects['staff/handle']
}))(Form.create()(StaffPage))

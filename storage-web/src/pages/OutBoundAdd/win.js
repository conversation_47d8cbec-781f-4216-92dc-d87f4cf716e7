/*
 * @Author: nightowl
 * @Date: 2019-08-27 14:41:40
 * @LastEditors: nightowl
 * @LastEditTime: 2019-09-26 10:29:09
 * @Description:
 */
import { useEffect, useState } from 'react'
import { connect } from 'dva'
import { Modal, Form, Input, Cascader, Message, Table, Button, Select, Icon } from 'antd'
import Service from './service'
import Area from '@/components/Area'
import { View } from 'bonc-element'
const FormItem = Form.Item
const Option = Select.Option;
const TextArea = Input.TextArea;

const AddWin = ({ addList, TreeSelect, newList, wulist, list, selectList, close, form, totalCount, title, chooseInfo, chooseJudge, edith, info, dispatch, history, loading, permissionsList, storeId,leaveWay }) => {
  const [searchParams, setSearchParams] = useState({ page: 1, limit: 10 });
  const formItemLayout = {
    labelAlign: 'left',
    labelCol: {
      xs: { span: 44 },
      sm: { span: 2 }
    },
    wrapperCol: {
      xs: { span: 4 },
      sm: { span: 9 }
    }
  }
  const [currentPage, setPage] = useState(1)
  const pageLimit = 4;
  const handleClose = () => {
    dispatch({
      type: "outBoundAdd/setAcInfo",
      payload: { info: null }
    })
    let a = []
    dispatch({
      type: "outBoundAdd/setChooseInfo",
      payload: {
        chooseInfo: a,
        chooseJudge: false
      }
    })
    close && close()
    form.resetFields()
  }


  useEffect(() => {
    // resetForm();
    getWuList({ page: currentPage, limit: pageLimit, storeId: storeId, leaveWay: leaveWay })
    //getSelect({ page: currentPage, limit: pageLimit })

  }, [])
  const onChange = (page) => {
    setPage(page)
    let currPage = page;
    getWuList({ page: currPage, limit: pageLimit, storeId: storeId, leaveWay: leaveWay });
  }
  const getWuList = (params) => {
    dispatch({
      type: 'outBoundAdd/getWuList',
      payload: params
    })
  }
  const getSelect = (params) => {
    dispatch({
      type: 'outBoundAdd/getSelectList',
      payload: params
    })
  }
  const rowSelection = {

    onChange: (selectedRowKeys) => {
      setSelectedRowKeys({ ids: selectedRowKeys });
    },
    // getCheckboxProps: record => ({
    //   disabled: record.name === 'Disabled User', // Column configuration not to be checked
    //   name: record.name,
    // }),
  };
  const handleOk = (e) => {

    e.preventDefault()
    for (var i = 0; i < chooseInfo.length; i++) {
      newList.push(chooseInfo[i])
    }
    dispatch({
      type: "outBoundAdd/setNewList",
      payload: {
        newList: newList
      }
    })
    handleClose()
  }

  const handleChoose = (id) => {
    console.log(id)
    console.log('wuwuwu',wulist)
    for (var i = 0; i < wulist.length; i++) {
      if (wulist[i].materielId == id) {

        chooseInfo.push(wulist[i])
        dispatch({
          type: "outBoundAdd/setChooseInfo",
          payload: {
            chooseInfo: chooseInfo,
            chooseJudge: false
          }
        })
        dispatch({
          type: "outBoundAdd/setChooseInfo",
          payload: {
            chooseInfo: chooseInfo,
            chooseJudge: true
          }
        })
        console.log('chooseInfo',chooseInfo)
      }
    }
  };
  const handleDelete = (id) => {
    for (var i = 0; i < chooseInfo.length; i++) {
      if (chooseInfo[i].materielId == id) {
        chooseInfo.splice(i, 1);
        dispatch({
          type: "outBoundAdd/setChooseInfo",
          payload: {
            chooseInfo: chooseInfo,
            chooseJudge: false
          }
        })
        dispatch({
          type: "outBoundAdd/setChooseInfo",
          payload: {
            chooseInfo: chooseInfo,
            chooseJudge: true
          }
        })
      }
    }

  };
  const columns_all = [
    {
      title: '序号', render: (text, record, index) => {
        return (
          (currentPage - 1) * pageLimit + (index + 1)
        )
      }
    },
    { dataIndex: 'materielName', title: '物料名称' },
    { dataIndex: 'materielNo', title: '物料编号' },
    { dataIndex: 'materielBrandValue', title: '品牌' },
    { dataIndex: 'positionName', title: '仓位' },
    {
      title: '操作',
      // dataIndex: 'id',

      // fixed: 'right',
      render: (_, record) => {
        return <div>
          <span className="handle-2" onClick={() => { handleChoose(record.materielId) }} >选择</span>
        </div>
      }

    }
  ]
  const postData = (param) => {


    if (edith) {//编辑
      dispatch({
        type: "inBoundAdd/updateAc",
        payload: param
      })
    } else {//新增
      dispatch({
        type: "inBoundAdd/addAc",
        payload: param
      })
    }
  }
  const onChange1 = (value) => {
    let materielSn = form.getFieldValue('materielSn');
    let materielName = form.getFieldValue('materielName');
    if (materielName == undefined) {
      materielName = ''
    }
    if (materielSn == undefined) {
      materielSn = ''
    }
    console.log(materielSn);
    setPage(1)
    getWuList({materielName: materielName,materielSn: materielSn, page: 1, limit: 4, storeId: storeId })
  };
  const onChangeValue = (value) => {
    console.log(value);
  };
  const displayRender = (label) => {
    return label[label.length - 1];
  }



  const { getFieldDecorator } = form
  return <Modal width={700}
    maskClosable={false}
    onCancel={handleClose}
    title={title}
    visible={addList}
    footer={[
      <Button key="back" onClick={handleClose}>取消</Button>,
      <Button key="submit" type="primary" onClick={handleOk} loading={loading}>确定</Button>,
    ]}>
    <Form layout='inline'>
      <FormItem >
        {getFieldDecorator('materielName')(
          <Input placeholder='请输入物料名称' />
        )}
      </FormItem>
      <FormItem >
        {getFieldDecorator('materielSn')(
          <Input placeholder='请输入SN号' />
        )}
      </FormItem>
      {/* <FormItem label='类型'>
        {getFieldDecorator('materielTypeId')(
          <Cascader
            fieldNames={{ label: 'name', value: 'id' }}
            options={selectList}
            // onChange={onChange1}
            placeholder="请选择"
          />,
        )}
      </FormItem> */}
      <View style={{ display: 'inline-block', justifyContent: ' flex-end', paddingTop: '3px', marginLeft: '10px' }}>
        <Button icon='search' type='primary' style={{ marginBottom: '10px' }} onClick={onChange1} >搜索</Button>
      </View>
    </Form>
    <Table columns={columns_all} dataSource={wulist} rowKey={item => item.key}
      pagination={{ current: currentPage, total: totalCount, onChange: onChange, pageSize: pageLimit }} loading={loading} />
    {/* <View>已选物料：</View> */}
    <View style={{
      display: 'flex',
      justifyContent: 'flex-start',
      flexWrap: 'wrap',
      // marginLeft: '70px'
    }}>已选物料：
      {chooseJudge ?
        chooseInfo.map(item => {
          return <span style={{ marginRight: '25px' }}>{item.materielType}&nbsp;{item.materielSpec}&nbsp;{item.materielBrandValue}&nbsp;&nbsp;<Icon type="close-square" onClick={() => { handleDelete(item.materielId) }} /></span>

        })

        : null}
    </View>
  </Modal>
}

export default connect(({ outBoundAdd, menu, loading }) => ({
  list: outBoundAdd.list,
  addList: outBoundAdd.addList,
  selectList: outBoundAdd.selectList,
  wulist: outBoundAdd.wulist,
  newList: outBoundAdd.newList,
  info: outBoundAdd.info,
  totalCount: outBoundAdd.totalCount,
  visible: outBoundAdd.visible,
  searchParams: outBoundAdd.searchParams,
  searchVisible: outBoundAdd.searchVisible,
  permissionsList: menu.permissions,
  dispatchVisible: outBoundAdd.dispatchVisible,
  chooseInfo: outBoundAdd.chooseInfo,
  chooseJudge: outBoundAdd.chooseJudge,
  loading: loading.effects['outBoundAdd/getWuList'],
}))(Form.create({})(AddWin))

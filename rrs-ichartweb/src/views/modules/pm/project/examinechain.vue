<template>
  <div class="mod-config">
    <el-table
      :data="dataList"
      size="mini"
      :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
      border>
      <el-table-column
        prop="dealUserName"
        header-align="center"
        align="center"
        label="本环节处理人">
      </el-table-column>
      <el-table-column
        prop="upDealUsers"
        header-align="center"
        align="center"
        label="上环节处理人">
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id, scope.$index+1)">修改</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-button @click="addOrUpdateHandle()" size="mini" style="border:1px dashed #dcdfe6;color:#898b90;width:100%" icon="el-icon-plus"></el-button>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible && loadingOver" ref="addOrUpdate" :projectUuid="projectUuid" :projectId="projectId" :prevPerson="dataList" :isFirst="isFirst" :type="type" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>


<script>
import AddOrUpdate from './examinechain-add-or-update'
export default {
  props: {
    projectId: {
      type: Number
    },
    projectUuid: {
      type: String,
      required: true
    },
    type: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      addOrUpdateVisible: false,
      dataListLoading: false,
      dataList: [],
      prevPerson: [],
      loadingOver: false,
      isFirst: true
    }
  },
  created () {
    this.getDataList(true)
  },
  methods: {
    // 获取数据列表
    getDataList (isFirst) {
      if (!isFirst) { // not first request
        this.$emit('renderTree', this.type);
      }
      this.dataListLoading = true;
      let query = {'type': this.type};
      if (this.projectId) {
        query.projectId = this.projectId;
      } else if (this.projectUuid) {
        query.projectUuid = this.projectUuid;
      } else {
        console.warn('no projectUuid or projectId');
      }
      this.$http({
        url: this.$http.adornUrl('/pm/examinechain/listAll'),
        method: 'get',
        params: this.$http.adornParams(query)
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.data;
          this.isFirst = !this.dataList || this.dataList.length === 0;
          console.log('this.dataList', data.data);
          let users = data.users;
          if (this.dataList && this.dataList.length !== 0 && users && users.length !== 0) {
            this.dataList.map((item) => {
              let pids = item.pid;
              if (pids) {
                let pidsAry = this.dataList.filter((item) => { return pids.split(',').includes(String(item.id)); });
                let uids = pidsAry.map((item) => { return item.dealUserId }).join(',');
                let users_ = users.filter((item) => { return uids.split(',').includes(String(item.userId)); });
                users_ = users_.map((item) => { return item.username }).join(', ');
                item.upDealUsers = users_;
              }
            })
          }
        } else {
          this.dataList = [];
          this.isFirst = true;
        }
        this.dataListLoading = false;
        this.loadingOver = true;
      })
    },
    addOrUpdateHandle (id, index) {
      if (id) {
        this.isFirst = index === 1;
      } else {
        this.isFirst = this.dataList.length === 0;
      }
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id, index)
      })
    },
    deleteHandle (id) {
      this.$confirm('确定删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl(`/pm/examinechain/delete/${id}`),
          method: 'delete',
          data: {}
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 600,
              onClose: () => {
                this.getDataList()
              }
            });
          } else {
            this.$message.error(data.msg)
          }
        })
      }).catch(() => {})
    }
  },
  components: {
    AddOrUpdate
  }
}
</script>

<style scoped>
  .pa-title {
    margin-top: 20px;
    height: 36px;
    line-height: 30px;
    border-bottom: 1px solid #efefef;
  }
</style>

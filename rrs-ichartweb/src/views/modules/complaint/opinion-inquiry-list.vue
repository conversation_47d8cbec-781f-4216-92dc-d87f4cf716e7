<template>
  <div v-if="opinionInquiryList.length">
    <el-row class="row-bg row-query-separator" style="padding:0px;margin-top: -22px;">
      <div style="height: 100%;width:8px;padding:3px;margin:10px 10px 10px 0px;background-color: #4EB3A5;display:inline-block">&nbsp;</div><span><b>处理信息</b></span>
    </el-row>
    <!-- 如果是自动流转，并且首发没还没有填写答案，显示申诉按钮-->
    <el-row>
      <div v-if="task.cirWay == 2 && opinionInquiryList.length > 1 && ((firstIssueItem && firstIssueItem.isWrite == 0))" class="text-right">
        <el-form label-width="120px" class="pa20" >
          <el-button type="primary" @click="showAppealInfo()">申诉</el-button>
        </el-form>
      </div>
    </el-row>
    <el-row>
      <div v-if="opinionInquiryList.length>1 && edges.length" class="dagre-graph-container">
        <DagreGraph v-if="nodes.length && edges.length" :nodes="nodes" :edges="edges" ref="dagreTempShow"></DagreGraph>
      </div>
    </el-row>
    <el-row>
      <el-table
        size="small"
        :row-class-name="getRowClass"
        border
        v-if="(opinionInquiryList.length > 1 || (view == 2 && task.complaintStatus == 5)) "
        :data="opinionInquiryList"
        style="width: 100%;text-align: center;margin-bottom: 30px;">
        <el-table-column
          type="index"
          header-align="center"
          label="序号">
        </el-table-column>
        <el-table-column
          prop="dealDeptName"
          header-align="center"
          label="处理部门">
        </el-table-column>
        <el-table-column
          prop="dealUserName"
          header-align="center"
          label="处理人">
        </el-table-column>
        <el-table-column
          prop="updateTime"
          header-align="center"
          label="提交时间">
          <template slot-scope="scope">
            <span v-if="scope.row.ideaStatus == 1" style="cursor:pointer" size="small">{{scope.row.updateTime}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="consultIdea"
          header-align="center"
          :show-overflow-tooltip="true"
          label="意见">
          <template slot-scope="scope">
            <span v-if="scope.row.ideaStatus == 1 || (scope.row.dealUserId == userId)" style="cursor:pointer" size="small">{{scope.row.consultIdea}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          header-align="center"
          label="附件">
          <template slot-scope="scope">
            <span v-if="scope.row.name && (scope.row.ideaStatus == 1 || (scope.row.dealUserId == userId))" size="small" style="cursor:pointer" @click="downLoadTempFile(scope.row)">{{scope.row.name}}</span>
          </template>
        </el-table-column>
        <el-table-column
          header-align="center"
          label="操作">
          <template slot-scope="scope" v-if="view == 1">
            <el-button type="text" size="small" v-if="dealType == 5 && (userId == scope.row.dealUserId) && scope.row.isWrite == 0 && scope.row.isPrevFill == 1" @click="writeIdea(scope.row)">填写意见</el-button>
            <el-button type="text" size="small" v-if="dealType == 5 && (userId == scope.row.dealUserId) && (scope.row.isWrite == 1 && scope.row.isWrite != 2) && scope.row.isPrevFill == 1 && scope.row.ideaStatus == 0" @click="updateWriteIdea(scope.row)">修改意见</el-button>
            <!--首发人指派，但是 如果有征询的人了，就不可以在指派-->
            <el-button type="text" size="small" v-if="dealType == 5 && ((task.dealUserId == userId && userId == scope.row.dealUserId)) && scope.row.isFirstIssue == 1 && scope.row.isWrite != 2 && scope.row.isPrevFill == 1 && scope.row.ideaStatus == 0" @click="isUserWriteIdea(scope.row,1)">指派</el-button>
            <!--其他人开放指派，但是 如果有征询的人了，就不可以在指派-->
            <el-button type="text" size="small" v-if="dealType == 5 && task.dealUserId != userId && (userId == scope.row.dealUserId) && scope.row.isFirstIssue == 0 && scope.row.isWrite != 2 && scope.row.isPrevFill == 1 && scope.row.ideaStatus == 0" @click="isUserWriteIdea(scope.row,2)">指派</el-button>
            <el-button type="text" size="small" v-if="dealType == 5 && (userId == scope.row.dealUserId) && scope.row.isWrite != 2 && scope.row.isPrevFill == 1 && scope.row.ideaStatus == 0" @click="zxTask(scope.row)">征询</el-button>
            <el-button type="text" size="small" v-if="dealType == 5 && (userId == scope.row.dealUserId) && (scope.row.isWrite == 1 && scope.row.isWrite != 2) && scope.row.isPrevFill == 1 && scope.row.ideaStatus == 0" @click="saveIdea(scope.row)">提交意见</el-button>
            <!--任务处理人有所有添加子征询人的权限，当前处理人有权限添加自己分支下面的 。-->
            <el-button type="text" size="small" v-if="dealType == 5 && scope.row.isChild == 1 && (scope.row.isWrite != 1 && scope.row.isWrite != 2)" @click="zxTask(scope.row)">添加子征询人</el-button>
            <el-button type="text" size="small" v-if="dealType == 5 && scope.row.isChild == 1 && (scope.row.isWrite != 1 && scope.row.isWrite != 2)" @click="updatePid(scope.row)">修改上级</el-button>
            <el-button type="text" size="small" v-if="dealType == 5 && scope.row.isChild == 1 && (scope.row.isWrite != 1 && scope.row.isWrite != 2)" @click="delTask(scope.row)">删除</el-button>
            <el-button type="text" size="small" v-if="dealType == 5 && scope.row.isChild == 2 && (scope.row.isWrite != 1 && scope.row.isWrite != 2)" @click="pressSbTo(scope.row)">催办</el-button>
            <!--<el-button type="text" size="small" @click="writeIdeaView(scope.row)">查看</el-button>-->
          </template>
        </el-table-column>
      </el-table>
    </el-row>



    <!--填写终极意见-->
    <div v-if="(task.dealUserId === userId) && dealType == 5 && view == 1 && showLastIdea">
      <el-form ref="formLast" :rules="dataRule" :model="formLast" label-width="120px" class="pa20" >
        <el-form-item label="最终处理意见:" prop="dealIdeaLast" class="common_opinionlist">
          <el-input type="textarea" rows="5" v-model="formLast.dealIdeaLast" @input="sCountNum(formLast.dealIdeaLast,'zzyj')" maxlength=800> </el-input>
          <span>{{zzyj}}/800</span>
        </el-form-item>
        <el-form-item label="附件:">
          <el-upload
            class="upload-demo"
            ref="upload"
            :action="importFileUrl"
            :data="uploadData"
            :multiple="true"
            :limit="1"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :onError="uploadError"
            :onSuccess="uploadSuccess"
            :file-list="fileList"
            :headers="token"
            :name="fileList.filename"
            :auto-upload="true"
            :accept="acceptFileType"
            :beforeUpload="beforeAvatarUpload">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <span slot="tip" class="el-upload__tip">（只能上传word、excel、pdf、jpg文件等，且不超过10兆）</span>
          </el-upload>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="tj(1)">保存</el-button>
          <el-button type="primary" @click="tj(2)">提交</el-button>
          <el-button v-if="opinionInquiryList.length === 1" type="primary" @click="isUserWriteIdea(opinionInquiryList[0],1)">指派</el-button>
          <el-button v-if="opinionInquiryList.length === 1" type="primary" @click="zxTask(opinionInquiryList[0])">征询</el-button>
          <el-button v-if="opinionInquiryList.length === 1" type="primary" @click="showAppealInfo()">申诉</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-------------------------------------指派---------------------------------->
    <el-dialog width="50%" :visible.sync="zpTasklog" :close-on-click-modal="false" :modal-append-to-body='false'>
      <div class="pa-title">
        <h2>指派</h2>
      </div>
      <el-form ref="formDealZX" :model="formDealZX" label-width="120px" class="pa20">
        <el-form-item label="指派意见:" prop="dealIdea" class="common_opinionlist">
          <el-input type="textarea" rows="5" v-model="formDealZX.dealIdea" @change="validateDataZX()" @input="sCountNum(formDealZX.dealIdea,'zpyj')" maxlength=800></el-input>
          <span>{{zpyj}}/800</span>
        </el-form-item>
        <!--<el-form-item label="处理部门:" prop="dealDeptId">
          <mgdept v-model="formDealZX.dealDeptId" @validChange="validateDataZX(1)" style="width: 50%"></mgdept>
        </el-form-item>-->
        <el-form-item label="处理人:" prop="dealUserId">
          <!--<mguser ref="userSelect"
                  @change="validateDataZX"
                  v-model="formDealZX.dealUserId"
                  :deptId="formDealZX.dealDeptId"
                  style="width: 40%"
          ></mguser>-->
          <dept-user-selector-ps style="width: 400px;" ref="deptUserSelect"
                                 v-model="formDealZX.dealUserId"
                                 @sendUserInfo="sendUserInfo($event)"
                                 :uname="formDealZX.dealUserName">
          </dept-user-selector-ps>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :disabled="!formZX" @click="saveZXorZP()">确定</el-button>
          <el-button @click="delogClose()">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <!--申诉弹窗-->
    <el-dialog width="45%" :visible.sync="showAppealSS" :close-on-click-modal="false" :modal-append-to-body='false' center>
      <div>
        <el-form :model="formSet" class="pa20">
          <el-form-item label="申诉意见:" label-width="120px" class="common_opinionlist">
            <el-input type="textarea" rows="5" v-model="formSet.dealIdea" @input="sCountNum(formSet.dealIdea,'ssyj')" maxlength=800> </el-input>
            <span>{{ssyj}}/800</span>
          </el-form-item>
        </el-form>
        <div class="text-center">
          <el-button type="primary" @click="appealReturn()">申诉</el-button>
          <el-button type="warning" @click="showAppealSS = false">取消</el-button>
        </div>
      </div>
    </el-dialog>
    <!--弹窗-->
    <!--填写意见-->
    <accept-write-idea v-if="acceptWriteIdeaVisible" ref="acceptWriteIdea" @refreshDataList="initAuditList"></accept-write-idea>
    <!--查看填写意见-->
    <accept-write-view v-if="acceptWriteIdeaViewVisible"  ref="acceptWriteView"></accept-write-view>

    <!--征询-->
    <zx-task-user v-if="zxVisible" ref="zxTaskUser" @refreshDataList="initAuditList"></zx-task-user>
    <!--修改上级-->
    <updatePidPage v-if="updatePidVisible" ref="toUpdatePid" @refreshDataList="initAuditList"></updatePidPage>
  </div>

</template>
<script>
  import acceptWriteIdea from './accept-write-idea';
  import acceptWriteView from './accept-write-view';
  import treeChartFirst from '../echart/tree-chart-first';
  // import { treeDataTranslate } from '@/utils';
  import { Loading } from 'element-ui';
  import zxTaskUser from './zx-task-user';
  import updatePidPage from './update-pid';
  import mgdept from '@/components/dept/mgtree'
  import mguser from '@/components/user/user-select'
  import deptUserSelectorPs from '@/components/user/dept-user-selector-ps'
  /**
   * 流程图
   */
  import DagreGraph from '../echart/dagre-template'
  export default {
    name: 'opinionInquiryList',
    components: {
      deptUserSelectorPs,
      mgdept,
      mguser,
      acceptWriteIdea,
      acceptWriteView,
      treeChartFirst,
      zxTaskUser,
      DagreGraph,
      updatePidPage
    },
    props: {
      dealType: {
        type: Number,
        required: true
      },
      view: {
        type: Number,
        required: true
      },
      task: {
        type: Object,
        default: function () {
          return {};
        }
      }
    },
    data() {
      return {
        formSet: {
          id: '',
          dealDeptId: '', // 部门id
          dealUserId: '',
          dealIdea: '',
          complaintStatus: ''
        },
        // 查询条件
        formInline: {
        },
        // 指派
        formDealZX: {
          dealUserId: null,
          dealDeptId: null,
          dealUserName: '',
          dealIdea: ''
        },
        // taskItem: {},
        // bizType: '',
        userId: '',
        opinionInquiryList: [],
        zpTasklog: false, //征询弹出框显示
        formZX: false, // 征询按钮的是否可点击
        deptList: [],
        userList: [],
        clickType: '', // 判断是征询还是指派： 1 征询 2 指派
        acceptWriteIdeaVisible: false,
        acceptWriteIdeaViewVisible: true,
        zxVisible: false,
        updatePidVisible: false,
        isHasZP: false,
        opinionIntem: [],
        isTaskUser: 1, // 判断当前人是否是任务处理人，用于指派
        // ------------------------最终提交意见
        formLast: {
          dealIdeaLast: ''
        },
        fileList: [],
        acceptFileType: '.xls,.xlsx,.doc,.docx,.ppt,.pptx,.jpg,.jpeg,.png,.JPG,.JPEG,.pdf',
        // 文件上传
        importFileUrl: this.$http.adornUrl('/system/sys/attach/upload'),
        token: {token: this.$cookie.get('token')},
        showLastIdea: false, // 显示最终处理意见
        uploadData: {
          bussiId: '',
          moduleId: 6,
          hashCode: '' // MD5
        },
        treeData: [],
        nodes: [],
        edges: [],
        // 验证
        dataRule: {
          dealIdeaLast: [
            { required: true, message: '处理意见必填', trigger: 'blur' }
          ]
        },
        showAppealSS: false,
        // -------------------意见判断
        zzyj: 0,
        zpyj: 0,
        ssyj: 0,
        lastIdeaItem: [],
        firstIssueItem: [] // 首发人
      }
    },
    created: function () {
      this.$destroy();
      this.userId = this.getLoginUserId();
      this.initAuditList();
      // 当投诉处理人的时候初始化附件bussiId ,因为可能是最终提交意见
      if (this.task.dealUserId === this.userId) {
        this.getBussiId();
        this.getLastIdeaItem()
      }
    },

    methods: {
      initAuditList: function () {
        this.edges = []
        this.nodes = []
        this.showLastIdea = false
        this.getFlowRelation().then(() => {
          this.getListData()
        })
        this.getFirstIssue()
      },
      // 获取首发人
      getFirstIssue () {
        return this.$http({
          url: this.$http.adornUrl('/complain/opinioninquiry/getFirstIssue'),
          method: 'get',
          params: this.$http.adornParams({
            taskAccpetId: this.task.id
          })
        }).then(({data}) => {
          this.firstIssueItem = data && data.code === 0 ? data.opinionInquiry : []
        })
      },
      // 获取最终处理意见
      getLastIdeaItem: function() {
        this.$http({
          url: this.$http.adornUrl('/complain/complainttaskprocessing/list'),
          method: 'get',
          params: this.$http.adornParams({
            taskAccpetId: this.task.id,
            dealTypeValue: 10
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.datas.length > 0) {
              this.lastIdeaItem = data.datas[0]
              this.formLast.dealIdeaLast = data.datas[0].dealIdea
              this.sCountNum(this.formLast.dealIdeaLast, 'zzyj')
              this.uploadData.bussiId = data.datas[0].attachId
              this.getFileList()
            }
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      // 获取附件
      getFileList () {
        this.$http({
          url: this.$http.adornUrl('/sys/attach/select'),
          method: 'get',
          params: this.$http.adornParams({bussi_id: this.uploadData.bussiId})
        }).then(({data}) => {
          this.fileList = data && data.code === 0 ? data.list : []
        })
      },
      // 获取列表数据
      getListData() {
        return this.$http({
          url: this.$http.adornUrl('/complain/opinioninquiry/list'),
          method: 'get',
          params: this.$http.adornParams({
            taskAccpetId: this.task.id,
            // dealType: this.dealType,
            orderBy: 'order_id,id'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            // 数据处理，判断当前人的分支节点打标识，添加子征询人。
            this.findChild(data.datas)
            this.opinionInquiryList = data.datas;
            if (this.opinionInquiryList.length === 1) {
              this.showLastIdea = true
            }
            this.isWriteAllOk()
            // 递归处理数据
            if (this.opinionInquiryList.length > 1) {
              data.datas.forEach((item) => {
                if (item.isWrite !== 2) {
                  if (item.ideaStatus === 1) {
                    this.nodes.push({ id: item.id, state: 'running', label: item.dealUserName, class: 'BashOperator' })
                  } else {
                    this.nodes.push({ id: item.id, state: 'failed', label: item.dealUserName, class: 'BashOperator' })
                  }
                }
              })
              this.$nextTick(() => {
                if (this.$refs['dagreTempShow']) {
                  this.$refs['dagreTempShow'].reloadDagreTemp(this.nodes, this.edges)
                } else {
                  console.log('noshouwdagre20190102')
                }
              })
              /*let a = []
              data.datas.forEach((item) => {
                if (item.isWrite !== 2) {
                  var cIdea = item.consultIdea ? item.consultIdea : ' '
                  a.push(
                    { name: item.dealUserName,
                      dname: item.dealDeptName,
                      flag: item.ideaStatus,
                      value: cIdea,
                      id: item.id,
                      pid: item.pid,
                      itemStyle: {}
                    })
                }
              })
              this.treeData = treeDataTranslate(a, 'id', 'pid')
              this.$nextTick(() => {
                this.$refs['treeChartFY'].reloadChart(this.treeData)
              })*/
            }
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      // 获取链条关系
      getFlowRelation() {
        return this.$http({
          url: this.$http.adornUrl('/complain/opinioninquiry/listTwo'),
          method: 'get',
          params: this.$http.adornParams({
            taskAccpetId: this.task.id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.datas.length > 0) {
              data.datas.forEach((item) => {
                this.edges.push({from: item.fromId, to: item.toId})
              })
            }
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      // 递归处理数据，修改子集属性
      findChild (data) {
        data.forEach((item) => {
          if (((item.dealUserId === this.userId)) && item.ideaStatus === 0) {
            this.updateChild(data, item.dealUserId)
          } else if (((item.dealUserId === this.userId)) && item.ideaStatus === 1) {
            // 如果提交之后，判断孩子是否提交，没有提交催办
            this.updateChildCB(data, item.dealUserId)
          }
        })
      },
      updateChild (data, dealUserId) {
        //let that_ = this
        data.forEach((item) => {
          if (item.upUserId === dealUserId) {
            item.isChild = 1
            //that_.updateChild(data, item.upUserId)
          }
        })
      },
      // 催办状态标记
      updateChildCB (data, dealUserId) {
        //let that_ = this
        data.forEach((item) => {
          if (item.upUserId === dealUserId && item.ideaStatus === 0) {
            item.isChild = 2
            //that_.updateChildCB(data, item.upUserId)
          }
        })
      },
      // 过滤投诉渠道
      formatterHandleType (row, column) {
        let handleTypeName = ''
        if (row.handleType === 1) {
          handleTypeName = '征询'
        } else if (row.handleType === 2) {
          handleTypeName = '指派'
        }
        return handleTypeName;
      },
      // 过滤是否首发部门
      formatterFirstIssue (row, column) {
        let isTrue = '否'
        if (row.isFirstIssue === 1) {
          isTrue = '是'
        }
        return isTrue;
      },
      // --------------------------------------投诉征询催办
      pressSbTo (item) {
        this.$confirm('确认是否执行此操作?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complain/complainttaskprocessing/saveDealInfo'),
            method: 'post',
            data: this.$http.adornParams({
              taskAccpetId: this.task.id, // 受里任务的
              dealDeptId: item.dealDeptId,
              dealUserId: item.dealUserId,
              appealJustic: 12 // 投诉征询催办
            })
          }).then(({data}) => {
            loadingInstance.close()
            if (data && data.code === 0) {
              this.$message({
                type: 'success',
                message: '操作成功!'
              });
            } else {
              this.$message({
                type: 'false',
                message: '操作失败!'
              });
            }
          }).catch(() => {
            loadingInstance.close()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      // -----------------------------------------征询
      zxTask (item) {
        // 征询
        this.zxVisible = true;
        this.$nextTick(() => {
          this.$refs.zxTaskUser.init(item)
        })
      },
      zpTask (item) {
        this.zpyj = 0
        this.clickType = '2' // 指派
        this.zpTasklog = true
        this.formDealZX.dealDeptId = null;
        this.formDealZX.dealUserId = null;
        this.formDealZX.dealUserName = null;
        this.formDealZX.dealIdea = '';
        this.opinionIntem = item
        //this.initReceiveDeptList(1);
      },
      validateDataZX(clean) {
        // if (clean) {
        //   this.formDealZX.dealUserId = null
        // }
        this.formZX = false
        if (this.formDealZX.dealIdea !== '' && this.formDealZX.dealUserId !== null && this.formDealZX.dealDeptId !== null) {
          this.formZX = true
        }
      },
      // 弹出框关闭
      delogClose () {
        this.zpTasklog = false
        this.formZX = false
      },
      // ------------------------------------------------------------修改上级
      updatePid (item) {
        this.updatePidVisible = true;
        this.$nextTick(() => {
          this.$refs.toUpdatePid.init(item)
        })
      },
      // ----------------------------------------征询初始化 机构，用户
      // 查询所有部门：管理机构
      initReceiveDeptList(isClean) {
        if (isClean) {
          this.userList = [];
          this.formDealZX.dealDeptId = '';
          this.formDealZX.dealUserId = '';
        }
        this.$http({
          url: this.$http.adornUrl('/sys/dept/getTsDeptManger'),
          method: 'get',
          params: this.$http.adornParams({status: '1'})
        }).then(({data}) => {
          this.deptList = data && data.code === 0 ? data.list : [];
        })
      },
      initUserList(isClean) {
        if (isClean) {
          this.userList = [];
          this.formDealZX.dealUserId = '';
        }
        this.$http({
          url: this.$http.adornUrl('/system/user/listUser'),
          method: 'get',
          params: this.$http.adornParams({deptId: this.formDealZX.dealDeptId, status: '1'})
        }).then(({data}) => {
          this.userList = data && data.code === 0 ? data.list : [];
        })
      },
      // -----------------------------------------------------指派
      // -------------------------------------------------------获取机构人员
      sendUserInfo(userInfo) { // 添加首发人
        this.formDealZX.dealUserName = userInfo.username;
        this.formDealZX.dealUserId = userInfo.userId;
        this.formDealZX.dealDeptId = userInfo.deptId;
        this.formZX = false
        if (this.formDealZX.dealIdea !== '' && this.formDealZX.dealUserId !== null && this.formDealZX.dealDeptId !== null) {
          this.formZX = true
        }
      },
      // 转派其他人
      saveZXorZP () {
        if (this.formDealZX.dealIdea) {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complain/opinioninquiry/saveZXorZP'),
            method: 'post',
            data: this.$http.adornParams({
              taskAccpetId: this.task.id, // 受里任务的
              id: this.opinionIntem.id,
              pid: this.opinionIntem.pid,
              dealDeptId: this.formDealZX.dealDeptId,
              dealUserId: this.formDealZX.dealUserId,
              dealIdea: this.formDealZX.dealIdea,
              upUserId: this.opinionIntem.dealUserId,
              upDeptId: this.opinionIntem.dealDeptId,
              oldUpDeptId: this.opinionIntem.upDeptId,
              oldUpUserId: this.opinionIntem.upUserId,
              orderId: this.opinionIntem.orderId,
              isTaskUser: this.isTaskUser,
              clickType: this.clickType
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.zpTasklog = false
              this.formZX = false
              this.initAuditList();
              this.$message({
                type: 'success',
                message: '操作成功!',
                duration: 500,
                onClose: () => {
                  // @param : name  需要跳转tab的name属性
                  this.$changeRouter('complaint-pending')
                  this.closeThisTab();
                }
              });
            } else {
              this.$message({
                type: 'false',
                message: '操作失败!'
              });
            }
            loadingInstance.close()
          }).catch(() => {
            loadingInstance.close()
          })
        } else {
          this.$message({
            type: 'false',
            message: '意见必须填写!'
          });
        }
      },
      // ---------------------------------------------判断意见是否填写
      isUserWriteIdea (item, isSF) {
        this.isTaskUser = isSF
        // 20181227 放开指派限制
        this.zpTask(item)
        /*// 查看如果有征询则不可以 指派
        this.$http({
          url: this.$http.adornUrl('/complain/opinioninquiry/upPatherIsWrite'),
          method: 'get',
          params: this.$http.adornParams({
            taskAccpetId: this.task.id, // 受里任务的
            pid: item.id
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.countNull > 0) {
              this.$message({
                type: 'false',
                message: '征询后不可以指派!'
              });
            } else {
              //指派前先填写意见
              if (item.isWrite === 0) {
                this.$message({
                  type: 'false',
                  message: '请填写意见后在指派!'
                });
              } else {
                this.zpTask(item)
              }
            }
          }
        })*/
      },
      // 当前人提交自己的已经，提交后不能做其他操作
      saveIdea (item) {
        this.$confirm('提交后不可进行其他操作，确认是否提交?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complain/opinioninquiry/updateIdeaStatus'),
            method: 'post',
            data: this.$http.adornParams({
              id: item.id, // 受里任务的
              taskAccpetId: this.task.id,
              taskSfUserId: this.task.dealUserId,
              dealUserId: item.dealUserId,
              ideaStatus: '1'
            })
          }).then(({data}) => {
            loadingInstance.close()
            if (data && data.code === 0) {
              this.$message({
                type: 'success',
                message: '操作成功!'
              });
              this.initAuditList();
            } else {
              this.$message({
                type: 'false',
                message: '操作失败!'
              });
            }
          }).catch(() => {
            loadingInstance.close()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      // 删除
      delTask(item) {
        this.$confirm('确认是否删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complain/opinioninquiry/delZX'),
            method: 'post',
            params: this.$http.adornParams({
              id: item.id,
              taskAccpetId: item.taskAccpetId,
              upDeptId: item.upDeptId,
              upUserId: item.upUserId,
              pid: item.pid
            })
          }).then(({data}) => {
            loadingInstance.close()
            if (data && data.code === 0) {
              this.$message({
                type: 'success',
                message: '操作成功!'
              });
              this.initAuditList()
            } else {
              this.$message({
                type: 'false',
                message: '操作失败!'
              });
            }
          }).catch(() => {
            loadingInstance.close()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      // ----------------------------------------------填写内容
      writeIdea: function (item) {
        this.acceptWriteIdeaVisible = true;
        this.$nextTick(() => {
          this.$refs.acceptWriteIdea.init(item)
        })
      },
      // ----------------------------------------------修改填写内容
      updateWriteIdea: function (item) {
        this.acceptWriteIdeaVisible = true;
        this.$nextTick(() => {
          this.$refs.acceptWriteIdea.init(item)
        })
      },
      // --------------------------------------------------------计算数量
      sCountNum: function(num, type) {
        if (type === 'zzyj') {
          this.zzyj = 0 + num.length
        } else if (type === 'ssyj') {
          this.ssyj = 0 + num.length
        } else if (type === 'zpyj') {
          this.zpyj = 0 + num.length
        }
      },
      // ---------------------------------------------查看填写内容
      writeIdeaView: function (item) {
        this.acceptWriteIdeaVisible = true;
        this.$nextTick(() => {
          this.$refs.acceptWriteView.init(item)
        })
      },
      // ----------------------------------提交处理结果
      // 判断是否所有人都填写完成
      isWriteAllOk: function () {
        // 先判断是否还有人没填写意见
        this.$http({
          url: this.$http.adornUrl('/complain/opinioninquiry/getIdeaNull'),
          method: 'get',
          params: this.$http.adornParams({
            taskAccpetId: this.task.id // 受里任务的
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (data.countNull > 0 && this.opinionInquiryList.length > 1) {
              this.showLastIdea = false
            } else {
              this.showLastIdea = true
            }
          }
        })
      },
      tj (tjStatus) {
        let firstId = ''
        if (this.opinionInquiryList.length === 1) {
          firstId = this.opinionInquiryList[0].id
        }
        this.$refs['formLast'].validate((valid) => {
          if (valid) {
            if (tjStatus === 1) { //保存
              let loadingInstance = Loading.service({
                lock: true,
                text: 'Loading',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              this.$http({
                url: this.$http.adornUrl('/complain/complainttaskprocessing/saveDealInfo'),
                method: 'post',
                data: this.$http.adornParams({
                  taskAccpetId: this.task.id, // 受里任务的
                  dealIdea: this.formLast.dealIdeaLast,
                  bussiId: this.uploadData.bussiId,
                  dealUserId: this.task.dealUserId,
                  dealDeptId: this.task.dealDeptId,
                  accpetedUserId: this.task.accpetedUserId,
                  accpetedDeptId: this.task.accpetedDeptId,
                  id: this.lastIdeaItem.id,
                  tjStatus: tjStatus,
                  firstId: firstId,
                  appealJustic: 10 // 业务人员处理完成，提交到咨诉人员
                })
              }).then(({data}) => {
                if (data && data.code === 0) {
                  this.$message({
                    type: 'success',
                    message: '操作成功!'
                  });
                  this.getLastIdeaItem();
                } else {
                  this.$message({
                    type: 'false',
                    message: '操作失败!'
                  });
                }
                loadingInstance.close()
              }).catch(() => {
                loadingInstance.close()
              })
            } else {
              this.$confirm('确认是否提交最终意见?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                let loadingInstance2 = Loading.service({
                  lock: true,
                  text: 'Loading',
                  spinner: 'el-icon-loading',
                  background: 'rgba(0, 0, 0, 0.7)'
                });
                this.$http({
                  url: this.$http.adornUrl('/complain/complainttaskprocessing/saveDealInfo'),
                  method: 'post',
                  data: this.$http.adornParams({
                    taskAccpetId: this.task.id, // 受里任务的
                    dealIdea: this.formLast.dealIdeaLast,
                    bussiId: this.uploadData.bussiId,
                    accpetedUserId: this.task.accpetedUserId,
                    accpetedDeptId: this.task.accpetedDeptId,
                    dealUserId: this.task.dealUserId,
                    dealDeptId: this.task.dealDeptId,
                    id: this.lastIdeaItem.id,
                    tjStatus: tjStatus,
                    firstId: firstId,
                    appealJustic: 10 // 业务人员处理完成，提交到咨诉人员
                  })
                }).then(({data}) => {
                  if (data && data.code === 0) {
                    this.$message({
                      type: 'success',
                      message: '操作成功!',
                      duration: 500,
                      onClose: () => {
                        // @param : name  需要跳转tab的name属性
                        this.$changeRouter('complaint-pending')
                        this.closeThisTab();
                      }
                    });
                  } else {
                    this.$message({
                      type: 'false',
                      message: '操作失败!'
                    });
                  }
                  loadingInstance2.close()
                }).catch(() => {
                  loadingInstance2.close()
                })
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '取消'
                });
              });
            }
          }
        })
      },
      // -------------------------------------------------------附件上传 start
      submitUpload () {
        this.$refs.upload.submit();
      },
      handleRemove (file, fileList) {
        let id = file.response ? file.response.id : file.id;
        let bussiId = file.response ? file.response.bussiId : file.bussiId;
        // this.fileList = this.fileList.filter((item) => item.response.id !== id);
        this.$http({
          url: this.$http.adornUrl(`/sys/attach/${id}/${bussiId}`),
          method: 'delete',
          data: this.$http.adornData()
        }).then(({data}) => {
          if (data && data.code === 0) {
            console.log('删除成功');
          }
          //this.fileList = data && data.code === 0 ? data.list : [];
        })
      },
      handlePreview (file) {
        let id = file.response ? file.response.id : file.id;
        this.$downloadFileById(id);
      },
      getBussiId () {
        this.$http({
          url: this.$http.adornUrl('/sys/attach/genBussid'),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.uploadData.bussiId = data.bussiId;
          } else {
            this.$message.error({
              message: '获取数据失败！请稍后再试！',
              duration: 1500
            });
          }
        })
      },
      // 上传成功后的回调
      uploadSuccess (response, file, fileList) {
        if (response.code === 0) {
          console.log('上传文件', fileList);
          this.fileList = fileList
        } else {
          this.fileList.pop();
          this.$message.error({
            message: '上传失败，请重试！',
            duration: 1500
          });
        }
      },
      // 上传错误
      uploadError (response, file, fileList) {
        this.$message.error({
          message: '上传失败，请重试！',
          duration: 1500
        });
      },
      // 上传前对文件的大小的判断
      beforeAvatarUpload (file) {
        const fileExt = file.name.substr(file.name.lastIndexOf('.'));
        const isLt2M = file.size / 1024 / 1024 < 1000;
        const isValidExt = (this.acceptFileType.concat(',')).includes(fileExt.toLowerCase().concat(','));
        if (!isValidExt) {
          this.$message.warning({
            message: '只能上传word、excel、pdf、jpg等文件!',
            duration: 1500
          });
          return false;
        }
        if (!isLt2M) {
          this.$message.warning({
            message: '上传文件大小不能超过 10MB!',
            duration: 1500
          });
          return false;
        }
      },
      // 下载
      downLoadTempFile (item) {
        this.$downloadFileById(item.fileId);
      },
      // -------------------------------------------------------附件上传 end
      // ----------------------------关闭页签
      closeThisTab () {
        let com = this;
        let i = 0;
        while (!com.tabsCloseCurrentHandle && i < 20) {
          com = com.$parent;
          i++;
        }
        console.log('parent level = ', i); // router-view -> 4
        com.tabsCloseCurrentHandle();
        // this.$emit('closeCurrentTab')
      },
      // 设置行颜色
      getRowClass(row) {
        if (row.row.isWrite === 2) { // 指派后
          return 'ideaed-row';
        } else {  // others
          return 'idea-row';
        }
      },
      // ------------------------------------------------------------申诉
      // 申诉
      showAppealInfo () {
        this.ssyj = 0
        this.formSet.dealIdea = ''
        this.showAppealSS = true;
      },
      appealReturn () {
        // 如果申诉理由为空，这提示填写
        if (this.formSet.dealIdea) {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complain/complainttaskprocessing/saveDealInfo'),
            method: 'post',
            data: this.$http.adornParams({
              taskAccpetId: this.task.id, // 受里任务的
              dealIdea: this.formSet.dealIdea,
              dealDeptId: this.task.dealDeptId,
              dealUserId: this.task.dealUserId,
              accpetedDeptId: this.task.accpetedDeptId,
              accpetedUserId: this.task.accpetedUserId,
              appealJustic: 1
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.showAppealSS = false;
              this.$message({
                type: 'success',
                message: '操作成功!',
                duration: 500,
                onClose: () => {
                  // @param : name  需要跳转tab的name属性
                  this.$changeRouter('complaint-pending')
                  this.closeThisTab();
                }
              });
            } else {
              this.$message({
                type: 'false',
                message: '操作失败!'
              });
            }
            loadingInstance.close()
          }).catch(() => {
            loadingInstance.close()
          })
        } else {
          this.$message({
            type: 'false',
            message: '意见必须填写!'
          });
        }
      }
    }

  }
</script>
<style>
  .idea-row {
    color: #000000;
  }
  .ideaed-row {
    color: #BEBFC3;
  }
  .el-table__body, .el-table__footer, .el-table__header {
    width: 100% !important;
  }
  .dagre-graph-container {
    width: 100%;
    height: 300px;
  }
  .el-tooltip__popper {
    max-width: 500px;
    line-height: 130%;
  }
  .common_opinionlist .el-textarea__inner {
    width: 90%;
  }

</style>

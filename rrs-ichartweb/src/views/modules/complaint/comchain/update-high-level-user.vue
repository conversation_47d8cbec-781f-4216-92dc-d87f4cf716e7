<template>
  <el-dialog
    width="50%"
    title="修改上级"
    :close-on-click-modal="false"
    :visible.sync="innerVisible"
    append-to-body>
    <el-row type="flex">
      <el-col>
        <span>已选上级:</span>
        <el-tag
          v-for="tag in personList"
          :key="tag.userName"
          closable
          @close="handleClose(tag)">
          {{tag.userName}}
        </el-tag>
        <el-table
          class="pa20"
          :data="dataList"
          border
          size="small"
          @row-click="selectMajorPerson"
          :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
          style="width: 100%">
          <el-table-column
            label="序号"
            type="index"
            width="50">
          </el-table-column>
          <el-table-column
            prop="dealUserName"
            header-align="center"
            align="center"
            label="用户名">
          </el-table-column>
          <el-table-column
            prop="dealDeptName"
            header-align="center"
            align="center"
            label="部门">
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <div class="text-center">
      <el-button type="primary" @click="save()">确定</el-button>
      <el-button type="primary" @click="innerVisible=false">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
  // import {treeDataTranslate} from '@/utils'
  import { Loading } from 'element-ui';
  export default{
    data () {
      return {
        innerVisible: false,
        tagList: [],
        dataList: [],
        personList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        treeData: [],
        dataForm: {},
        record: {
          persons: [],
          busiId: '', // 业务编码
          thisId: '',
          processType: undefined,
          zdMoveType: undefined,
          upUserId: '',
          pid: '',
          personName: [],
          personTaskId: [] // 记录编号
        },
        details: [],
        defaultProps: {children: 'children', label: 'deptName'}
      }
    },
    methods: {
      init (item) {
        this.personList = []
        this.dataList = []
        this.record.busiId = item.busiId
        this.record.thisId = item.id
        this.record.pid = item.pid
        this.record.processType = item.processType
        this.record.zdMoveType = item.zdMoveType
        this.record.upUserId = item.upUserId
        this.record.persons = []
        this.record.personTaskId = []
        this.record.personName = []
        this.innerVisible = true
        this.initAuditList()
      },
      initAuditList: function () {
        this.$http({
          url: this.$http.adornUrl('/complainTwo/tsprocesschain/findByParamsPid'),
          method: 'get',
          params: this.$http.adornParams({
            id: this.record.thisId,
            busiId: this.record.busiId,
            processType: this.record.processType,
            zdMoveType: this.record.zdMoveType,
            upUserId: this.record.upUserId,
            dealUserId: this.record.upUserId,
            orderBy: 'create_time'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.datas
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      handleClose (tag) {
        for (var i = 0; i < this.personList.length; i++) {
          var userId = this.personList[i].userId;
          if (userId === tag.userId) {
            this.personList.splice(i, 1);
          }
        }
        this.record.persons.splice(this.record.persons.indexOf(tag.userId), 1);
        this.record.personTaskId.splice(this.record.personTaskId.indexOf(tag.id), 1);
        this.record.personName.splice(this.record.personName.indexOf(tag.userName), 1);
      },
      selectMajorPerson (row, event, column) {
        const index = this.personList.findIndex(item => item.userId === row.dealUserId);
        if (index < 0) {
          this.personList.push({userId: row.dealUserId, userName: row.dealUserName, id: row.id})
          this.record.persons.push(row.dealUserId)
          this.record.personTaskId.push(row.id)
          this.record.personName.push(row.dealUserName)
        }
      },
      save () {
        if (this.personList.length > 0) {
          if (this.record.persons) {
            this.record.persons = this.record.persons.join(',');
            this.record.personTaskId = this.record.personTaskId.join(',');
            this.record.personName = this.record.personName.join(',');
          }
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complainTwo/tsprocesschain/updateHighLevelUser'),
            method: 'post',
            params: this.$http.adornParams(this.record)
          }).then(({data}) => {
            if (data.code === 0) {
              this.$message({
                message: '操作成功',
                type: 'success',
                duration: 600,
                onClose: () => {
                  this.innerVisible = false
                  this.$emit('refreshDataList')
                }
              })
            }
            loadingInstance.close()
          }, (response) => {
            this.$message.error('操作失败')
            this.innerVisible = false
            this.$emit('refreshDataList')
            loadingInstance.close()
          })
        } else {
          this.$message({
            type: 'success',
            message: '请选择上级!'
          })
        }
      }
    }
  }
</script>
<style>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
</style>

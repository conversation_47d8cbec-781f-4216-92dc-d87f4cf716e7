<template>
  <el-dialog
    title="查看消息"
    width="40%"
    :close-on-click-modal="false"
    :close="closeHandle()"
    :visible.sync="visible">
    <el-form ref="dataForm" :model="dataForm" label-width="80px" class="pa20">
      <el-form-item label="发送人:">
        <span>{{dataForm.userNickname}}({{dataForm.username}})</span>
      </el-form-item>
      <el-form-item label="消息标题:">
        <span>{{dataForm.title}}</span>
      </el-form-item>
      <el-form-item label="消息内容:">
        <span>{{dataForm.content}}</span>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button type="warning" @click="visible = false">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import { Loading } from 'element-ui';
  export default{
    data () {
      return {
        visible: false,
        dataForm: {
          id: '',
          userNickname: '',
          username: '',
          content: '',
          title: ''
        }
      }
    },
    methods: {
      init (id) {
        this.dataForm.id = id || 0
        this.visible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].resetFields()
          if (this.dataForm.id) {
            let loadingInstance = Loading.service({
              lock: true,
              text: 'Loading',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$http({
              url: this.$http.adornUrl(`/sys/msg/${this.dataForm.id}`),
              method: 'get',
              params: this.$http.adornParams()
            }).then(({data}) => {
              loadingInstance.close();
              if (data && data.code === 0) {
                this.dataForm.content = data.info.content;
                this.dataForm.username = data.info.username;
                this.dataForm.title = data.info.title;
                this.dataForm.userNickname = data.info.userNickname;
              }
            })
          }
        })
      },
      closeHandle () {
        this.$emit('refreshDataList');
      }
    }
  }
</script>

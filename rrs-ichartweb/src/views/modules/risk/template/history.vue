<template>
  <div>
    <el-form :inline="true" :model="formInline" class="demo-form-inline" @keyup.enter.native="getQueryList()">
      <el-form-item label="模板名称">
        <el-input v-model="formInline.tempName" placeholder="名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="专业公司" >
        <com-selector ref="comSelector" v-model="formInline.deptId" clearable ></com-selector>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="pageIndex = 1;getQueryList()">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table
      size="small"
      :data="dataList"
      v-loading="dataListLoading"
      border
      :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
      style="width: 100%;text-align: center;">
      <el-table-column
        type="index"
        label="序号"
        width="80">
      </el-table-column>
      <el-table-column
        prop="tempName"
        label="模板名称"
        width="100">
      </el-table-column>
      <el-table-column
        prop="tempVersion"
        label="版本" width="90">
      </el-table-column>
      <!--<el-table-column-->
        <!--:formatter="formatter"-->
        <!--label="行业分类" width="110">-->
      <!--</el-table-column>-->
      <el-table-column
        label="专业公司" width="150"
        :formatter="formatter">
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="200">
      </el-table-column>
      <el-table-column
        prop="userName"
        label="创建人"
        width="100">
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="view(scope.row.id)">查看</el-button>
          <el-button type="text" size="small" v-if="scope.row.status !== 10" @click="update(scope.row.id)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>
<script>
  import comSelector from '@/components/dict/com-selector'

  export default {
    data () {
      return {
        formInline: {
          tempName: '',
          deptId: undefined,
          industryType: undefined
        },
        dataList: [],
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        templateTypeList: [],
        dialogFormVisible: false
      }
    },
    created () {
      let query = this.$route.query;
      console.log('query1', query);
      this.pageIndex = (query.page && Number(query.page)) || this.pageIndex;
      this.pageSize = (query.limit && Number(query.limit)) || this.pageSize;
      this.formInline.tempName = query.tempName || this.formInline.tempName;
      this.formInline.deptId = (query.deptId && Number(query.deptId)) || this.formInline.deptId;
      // this.getStatusList();
      this.getQueryList(1)
    },
    // 页面初始化
    activated () {
      this.getTemplateTypeList()
      this.getQueryList()
    },
    methods: {
      // 获取模板分类列表
      getTemplateTypeList () {
        this.$http({
          url: this.$http.adornUrl('/sys/dict/item'),
          method: 'get',
          params: this.$http.adornParams({
            'dictionaryId': '1'
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.templateTypeList = data.list
          } else {
            this.templateTypeList = []
          }
        })
      },
      // 获取数据列表
      getQueryList (isRefresh) {
        let query = {
          'page': this.pageIndex,
          'limit': this.pageSize,
          'tempName': this.formInline.tempName,
          'deptId': this.formInline.deptId
        };
        if (!isRefresh) {
          this.$router.push({query: query});
          let nameQuery = {name: this.$route.name, query: query};
          this.$store.commit('common/updateMainTabsQuery', nameQuery);
        }
        query.orderArr = [];
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/operational/template/getHistoryList'),
          method: 'get',
          params: this.$http.adornParams(query)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // 每页数
      sizeChangeHandle (val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getQueryList()
      },
      // 当前页
      currentChangeHandle (val) {
        this.pageIndex = val;
        this.getQueryList()
      },
      // 过滤类别
      formatter (row, column) {
        const res = this.$refs.comSelector.dictList.find(i => parseInt(i.deptId) === row.deptId);
        return res ? res.deptName : '';
      },
      view (id) {
        this.$router.push({name: 'viewTemplate', query: {tid: id, view: 1, history: 1}});
      },
      update (id) {
        this.$router.push({name: 'updateTemplate', query: {tid: id}});
      }
    },
    components: {
      comSelector
    }
  }
</script>

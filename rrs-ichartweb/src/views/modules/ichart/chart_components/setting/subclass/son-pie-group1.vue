<template>
  <div>
    <div>
      <setting-font :font="font" :set-font="setFont"/>
      <el-row class="data-name-title">
        <label>设置</label>
      </el-row>
      <el-row class="data-name-con">
        <label>类别</label>
        <el-select style="width: 60%" size="mini" v-model="categorySelect" placeholder="请选择类别"
                   @change="onCategorySelect">
          <el-option v-for="item in tableTitle"
                     :key="item.id"
                     :label="item.displayName"
                     :value="item.fieldName"></el-option>
        </el-select>
      </el-row>
      <el-row class="data-name-con">
        <label>数据列</label>
        <el-select style="width: 60%" size="mini" v-model="dataColumnSelect"
                   placeholder="请选择数据列" @change="onDataColumnSelect">
          <el-option v-for="item in tableTitle"
                     :key="item.id"
                     :label="item.displayName"
                     :value="item.fieldName"></el-option>
        </el-select>
      </el-row>
    </div>
    <el-row style="padding:5px 0px;text-align: right;font-size: 14px;">
        <span type="info" style="color: #15A193;cursor: pointer" @click="extract()">
            提取
          </span>
    </el-row>
    <el-row class="data-name-con" v-show="series_data.length">
      <label>总数列</label>
      <!--        <el-input size="mini" v-model="totalSelect" style="width: 50%"></el-input>-->
      <el-select style="width: 60%" size="mini" v-model="totalSelect"
                 placeholder="请选择总数列" @change="onTotalSelect">
        <el-option v-for="item in series_data"
                   :key="item.name"
                   :label="item.name"
                   :value="item.name"></el-option>
        <!--          <el-option v-for="(item , index) in tableTitle1"-->
        <!--                     :key="index"-->
        <!--                     :label="item.category"-->
        <!--                     :value="item.category"></el-option>-->
      </el-select>
    </el-row>
    <div>
      <el-row class="data-name-title">
        <el-col :span="18" style="text-align: left">系列</el-col>
        <el-col :span="6" style="padding:5px 0px;text-align: right;font-size: 14px;">
          <span type="info" style="color: #15A193;cursor: pointer" @click="addFixed()">
              新增
            </span>
        </el-col>
      </el-row>
      <div v-for="items in series_data"
           v-dragging="{ item: items, list: series_data}">
        <el-row style="margin: 3% 0 3% 6%">
          <div style="float: left">
            <el-input size="mini" v-model="items.name"
                      @blur="shiqu(items.name ,items.idx)"></el-input>
          </div>
          <el-color-picker
            v-model="items.color"
            :predefine="predefineColors"
            size="mini">
          </el-color-picker>
          <div style="float: right">
            <el-button
              type="text"
              @click="fixedDel(items.idx)">
              删除
            </el-button>
          </div>

        </el-row>
      </div>
      <el-row style="padding:5px 0px;text-align: right;font-size: 14px;
                      border-top:1px solid #ececec">
        <span type="info" style="color: #15A193;cursor: pointer" @click="refresh()">
            刷新
          </span>
      </el-row>
    </div>
  </div>
</template>

<script>
  import SettingFont from '../setting-font'

  export default {
    props: {
      params: Object,
      currObj: Object,
      tableTitle: Array
    },
    components: {
      SettingFont: SettingFont
    },
    data() {
      return {
        dataSelect: '',
        categorySelect: '',
        dataColumnSelect: '',
        totalSelect: '',
        tableTitle1: [],
        predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585'
        ],
        idx: 0,
        series_data: [],
        font: {
          color: '#333',
          fontSize: 12,
          fontStyle: 'normal'
        }
      }
    },
    methods: {
      DataName(name) {
        this.dataSelect = name;
        this.categorySelect = '';
        this.dataColumnSelect = '';
        this.totalSelect = '';
      },
      //设置里面的类别列
      onCategorySelect(val) {
        this.categorySelect = val;
      },
      //设置里面的数据列
      onDataColumnSelect(val) {
        this.dataColumnSelect = val;
        this.currObj._option.dsSetting.nameColName = this.categorySelect;
        this.currObj._option.dsSetting.valueColName.colName = this.dataColumnSelect;
        this.$http({
          url: this.$http.adornUrl('/ichart/qds/ds'),
          method: 'post',
          data: this.$http.adornParams({
            'params': this.params,
            'setting': this.currObj
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tableTitle1 = data.datas;
          }
        })
      },
      // 设置里面的总数列
      onTotalSelect(val) {
        this.totalSelect = val;
      },
      //系列新增
      addFixed() {
        
        this.series_data.push({
          idx: ++this.idx,
          name: '',
          color: '#' + Math.random().toString(16).slice(2, 8)
        })
      },
      //删除系列
      fixedDel(val) {
        this.series_data = this.series_data.filter((item) => {
          return item.idx != val
        })
      },
      //系列失去焦点
      shiqu(val, idx) {
        var result = this.series_data.filter((item) => {
          return item.name === val;
        })
        if (result.length > 1) {
          this.$message('名称重复，请重新输入！');
          this.series_data.forEach((item) => {
            if (item.idx === idx) {
              item.name = '';
            }
          });
        }
      },
      //提取
      extract() {
        this.series_data = []
        if (this.dataSelect === '') {
          this.$message('请选择数据名称');
        } else if (this.categorySelect === '') {
          this.$message('请选择设置中的类别列');
        }
        // else if (this.totalSelect === '') {
        //   this.$message('请选择设置中的总数列');
        // }
        else if (this.dataColumnSelect === '') {
          this.$message('请选择设置中的数据列');
        } else {
          this.currObj._option.dsSetting.nameColName = this.categorySelect;
          this.currObj._option.dsSetting.totalValName = this.totalSelect;
          this.currObj._option.dsSetting.valueColName.colName = this.dataColumnSelect;
          this.$http({
            url: this.$http.adornUrl('/ichart/qds/ds'),
            method: 'post',
            data: this.$http.adornParams({
              'params': this.params,
              'setting': this.currObj
            })
          }).then(({data}) => {
            for (var i = 0; i < data.datas.length; i++) {
              this.series_data.push({
                idx: ++this.idx,
                name: data.datas[i][this.categorySelect],
                color: '#' + Math.random().toString(16).slice(2, 8)
              })
            }
          })
        }
      },
      setFont(font) {
        this.font = font;
      },
      setStyle(item) {
        item["color"] = this.font.color;
        item["fontSize"] = this.font.fontSize;
        item["fontStyle"] = this.font.fontStyle;
        if (this.font.fontSize === "") {
          delete item.fontSize
        }
      },
      //刷新
      refresh() {
        if (this.dataSelect === '') {
          this.$message('请选择数据名称');
        } else if (this.categorySelect === '') {
          this.$message('请选择设置中的类别列');
        } else if (this.totalSelect === '') {
          this.$message('请选择设置中的总数列');
        } else if (this.dataColumnSelect === '') {
          this.$message('请选择设置中的数据列');
        } else {
          this.currObj._option.dsSetting.nameColName = this.categorySelect;
          this.currObj._option.dsSetting.totalValName = this.totalSelect;
          this.currObj._option.dsSetting.valueColName.colName = this.dataColumnSelect;
          this.currObj._option.series = this.series_data;
          this.currObj.option.textStyle = this.font;
          this.currObj.option.series.map(item => {
            item.label.normal["textStyle"] = {}
            this.setStyle(item.label.normal.textStyle)
            return item;
          })
          this.$emit('exeParentRefresh');
        }
      },
      EchoDisplay() {
        if (this.currObj._option.tableName !== '') {
          this.dataSelect = this.currObj._option.tableName;
          this.categorySelect = this.currObj._option.dsSetting.nameColName;
          this.totalSelect = this.currObj._option.dsSetting.totalValName;
          this.onDataColumnSelect(this.currObj._option.dsSetting.valueColName.colName)
          this.dataColumnSelect = this.currObj._option.dsSetting.valueColName.colName;
          this.series_data = this.currObj._option.series;
        }
      }
    },
    created(){
      if(this.currObj.option.hasOwnProperty("textStyle")){
        this.font = {...this.font, ...this.currObj.option.textStyle}
      }
    },
    mounted() {
      this.$dragging.$on('dragged', ({value}) => {
        this.series_data = value.list
      })
      this.EchoDisplay();
    },
  }
</script>

<style scoped>

</style>

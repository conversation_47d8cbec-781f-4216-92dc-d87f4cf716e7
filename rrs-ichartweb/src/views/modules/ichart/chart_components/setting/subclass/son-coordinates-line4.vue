<template>
  <div>
    <div class="pt20">
      <setting-font :font="font" :set-font="setFont"/>
    </div>
    <div>
      <el-row class="data-name-title">
        <label>坐标</label>
      </el-row>
      <el-row class="data-name-con">
        <label>X轴</label>
        <el-select style="width: 60%" size="mini" v-model="nameSelect" placeholder="请选择列名称" @change="onNameSelect">
          <el-option v-for="item in tableTitle"
                     :key="item.id"
                     :label="item.displayName"
                     :value="item.fieldName"></el-option>
        </el-select>
      </el-row>
      <el-row class="data-name-con">
        <label>单位</label>
        <el-input v-model="xAxisUnit" style="width: 60%" size="mini" placeholder="X轴单位"></el-input>
      </el-row>
    </div>
    <div>
      <el-row class="data-name-title">
        <el-col :span="18" style="text-align: left">系列</el-col>
        <!--        <el-col :span="6" style="padding:5px 0px;text-align: right;font-size: 14px;">-->
        <!--          <span type="info" style="color: #15A193;cursor: pointer" @click="addSetting()">-->
        <!--              新增-->
        <!--            </span>-->
        <!--        </el-col>-->
      </el-row>
      <div style="border-bottom: 1px solid #ececec;min-height: 270px"
           v-for="items in seriesList">
        <el-row style="margin: 3% 5%">
          <label style="display: inline-block;width: 25%">类型</label>
          <el-radio-group v-model="items.type1" size="mini" @change="onRadio(items.type1, items.idx)">
            <el-radio-button :label="1">普通</el-radio-button>
            <el-radio-button :label="2">表达式</el-radio-button>
          </el-radio-group>
        </el-row>
        <div style="width: 90%;float: left" v-if="items.type1===1">
          <el-row class="data-name-con">
            <label style="width: 30%">列名称</label>
            <el-select style="width: 68%" size="mini" v-model="items.columnName.colName" placeholder="请选择列名称"
                       @change="onValueSelect(items)">
              <el-option v-for="item in tableTitle"
                         :key="item.id"
                         :label="item.displayName"
                         :value="item.fieldName"></el-option>
            </el-select>
          </el-row>
          <el-row class="data-name-con">
            <label style="width: 30%">名称</label>
            <el-input v-model="items.columnName.name" style="width: 68%" size="mini" placeholder="名称"></el-input>
          </el-row>

          <el-row class="data-name-con">
            <label style="width: 30%">统计方式</label>
            <el-select style="width: 68%" v-model="items.columnName.statisticsType" size="mini" placeholder="请选择统计方式"
            >
              <el-option label="求和" value="sum"></el-option>
              <el-option label="平均值" value="avg"></el-option>
              <el-option label="数量" value="count"></el-option>
              <el-option label="数量(去重)" value="count_distinct"></el-option>
              <el-option label="最大值" value="max"></el-option>
              <el-option label="最小值" value="min"></el-option>
            </el-select>
          </el-row>
          <el-row class="data-name-con">
            <div style="float: left;height: 28px;line-height: 28px;width: 30%">颜色(低)</div>
            <el-color-picker
              v-model="color1"
              :predefine="predefineColors"
              size="mini">
            </el-color-picker>
          </el-row>
          <el-row class="data-name-con">
            <div style="float: left;height: 28px;line-height: 28px;width: 30%">颜色(高)</div>
            <el-color-picker
              v-model="color2"
              :predefine="predefineColors"
              size="mini">
            </el-color-picker>
          </el-row>
          <el-row class="data-name-con">
            <div style="float: left;height: 28px;line-height: 28px;width: 30%">渐变轴</div>
            <span>x轴</span>
            <el-switch
              v-model="dimension"
              active-color="#13ce66"
              inactive-color="#409EFF"
              active-value="1"
              inactive-value="0">
            </el-switch>
            <span>y轴</span>
          </el-row>

          <!--          <el-row class="data-name-con">-->
          <!--            <div style="float: left;height: 28px;line-height: 28px;width: 25%">区块颜色</div>-->
          <!--            <el-color-picker-->
          <!--              v-model="items.columnName.areaColor"-->
          <!--              :predefine="predefineColors"-->
          <!--              size="mini">-->
          <!--            </el-color-picker>-->
          <!--          </el-row>-->
        </div>
        <div style="width: 90%;" v-if="items.type1===2 && items.columnName.exp !== undefined">
          <el-row class="data-name-con">
            <label style="display:inline-block;width: 23%">名称</label>
            <el-input style="width: 68%" v-model="items.columnName.name" size="mini" placeholder="请输入名称"/>
          </el-row>
          <el-row class="data-name-con">
            <label style="display:inline-block;width: 23%">参数1</label>
            <el-select style="width: 68%" v-model="items.columnName.exp.val1" size="mini" placeholder="请选择列名称"
            >
              <el-option v-for="item in tableTitle"
                         :key="item.id"
                         :label="item.displayName"
                         :value="item.fieldName"></el-option>
            </el-select>
          </el-row>
          <el-row class="data-name-con">
            <label style="display:inline-block;width: 23%">操作符</label>
            <el-select style="width: 68%" size="mini" placeholder="请选择统计方式"
                       v-model="items.columnName.exp.op">
              <el-option label="+" value="+"></el-option>
              <el-option label="-" value="-"></el-option>
              <el-option label="*" value="*"></el-option>
              <el-option label="/" value="/"></el-option>
            </el-select>
          </el-row>
          <el-row class="data-name-con">
            <label style="display:inline-block;width: 23%">参数2</label>
            <el-select style="width: 68%" v-model="items.columnName.exp.val2" size="mini" placeholder="请选择列名称"
            >
              <el-option v-for="item in tableTitle"
                         :key="item.id"
                         :label="item.displayName"
                         :value="item.fieldName"></el-option>
            </el-select>
          </el-row>
          <el-row class="data-name-con">
            <div style="float: left;height: 28px;line-height: 28px;width: 25%">颜色(低)</div>
            <el-color-picker
              v-model="color1"
              :predefine="predefineColors"
              size="mini">
            </el-color-picker>
          </el-row>
          <el-row class="data-name-con">
            <div style="float: left;height: 28px;line-height: 28px;width: 25%">颜色(高)</div>
            <el-color-picker
              v-model="color2"
              :predefine="predefineColors"
              size="mini">
            </el-color-picker>
          </el-row>
          <el-row class="data-name-con">
            <div style="float: left;height: 28px;line-height: 28px;width: 25%">渐变轴</div>
            <span>x轴</span>
            <el-switch
              v-model="dimension"
              active-color="#13ce66"
              inactive-color="#409EFF"
              active-value="1"
              inactive-value="0">
            </el-switch>
            <span>y轴</span>
          </el-row>
          <!--          <el-row class="data-name-con">-->
          <!--            <div style="float: left;height: 28px;line-height: 28px;width: 25%">区块颜色</div>-->
          <!--            <el-color-picker-->
          <!--              v-model="items.columnName.areaColor"-->
          <!--              :predefine="predefineColors"-->
          <!--              size="mini">-->
          <!--            </el-color-picker>-->
          <!--          </el-row>-->
        </div>
        <!--        <div style="width: 10%;float: left;line-height: 240px">-->
        <!--          <el-button-->
        <!--            style="margin-left: 10%"-->
        <!--            type="text"-->
        <!--            @click="seriesDel(items.idx)">-->
        <!--            删除-->
        <!--          </el-button>-->
        <!--        </div>-->
      </div>
      <el-row class="refresh" style="padding:5px 0px;text-align: right;font-size: 14px;">
        <span type="info" style="color: #15A193;cursor: pointer" @click="refresh()">
            刷新
          </span>
      </el-row>
    </div>
  </div>
</template>

<script>
  import SettingFont from '../setting-font';
  const lineInRange = ['#D7DA8B', '#E15457']
  export default {
    props: {
      params: Object,
      currObj: Object,
      tableTitle: Array
    },
    components: {
      SettingFont
    },
    data() {
      return {
        dataSelect: '',
        nameSelect: '',
        xAxisUnit: '',
        valueSelect: '',
        predefineColors: [
          '#ff4500',
          '#ff8c00',
          '#ffd700',
          '#90ee90',
          '#00ced1',
          '#1e90ff',
          '#c71585'
        ],
        idx: 0,
        seriesList: [
          {
            data: [],
            type: 'line',
            columnName: {
              colName: '',
              statisticsType: 'sum',
              unit: '',
              color: lineInRange[1]
            },
            idx: 0,
            type1: 1,
          }
        ],
        font: {
          color: '#333',
          fontSize: 12,
          fontStyle: 'normal'
        },
        dimension: 0,
        color1: lineInRange[0],
        color2: lineInRange[1]
      }
    },
    methods: {
      DataName(name) {
        this.dataSelect = name
        this.nameSelect = ''
        for (var i = 0; i < this.seriesList.length; i++) {
          this.seriesList[i].columnName.colName = ''
        }
      },
      //X轴选中的值
      onNameSelect(val) {
        this.nameSelect = val
      },
      //系列列名称选中的值
      onValueSelect(items) {
        let col = this.tableTitle.filter(item => item.fieldName === items.columnName.colName)
        if (col && col.length > 0) {
          items.columnName.name = col[0].displayName;
        }
        this.valueSelect = items.columnName.colName
      },
      //单选按钮
      onRadio(type, idx) {
        let od = {
          data: [],
          type: 'line',
          columnName: {
            colName: '',
            statisticsType: 'sum',
            unit: '',
            color: '#' + Math.random().toString(16).slice(2, 8),
            // areaColor: '#' + Math.random().toString(16).slice(2, 8)
          },
          idx: 0,
          type1: 1,
        };
        let exp = {
          data: [],
          type: 'line',
          columnName: {
            exp: {
              val1: "",
              op: "/",
              val2: "",
            },
            color: '#' + Math.random().toString(16).slice(2, 8),
            // areaColor: '#' + Math.random().toString(16).slice(2, 8)
          },
          idx,
          type1: 2,
        }
        this.seriesList.forEach((item, index) => {
          if (item.idx === idx) {
            if (type === 1) {
              this.$set(this.seriesList, index, od)
            } else { // 2
              this.$set(this.seriesList, index, exp)
            }
            return false;
          }
        });
      },
      //系列新增
      addSetting() {
        this.seriesList.push({
          data: [],
          type: 'line',
          columnName: {
            colName: '',
            statisticsType: 'sum',
            unit: '',
            color: '#' + Math.random().toString(16).slice(2, 8),
            // areaColor: '#' + Math.random().toString(16).slice(2, 8)
          },
          idx: ++this.idx,
          type1: 1,
        })
      },
      //删除系列
      seriesDel(val) {
        this.seriesList = this.seriesList.filter((item) => {
          return item.idx != val
        })
      },
      //刷新图表
      refresh() {
        if (this.dataSelect === '') {
          this.$message('请选择数据名称');
        } else if (this.nameSelect === '') {
          this.$message('请选择X轴坐标');
        } else {
          this.currObj._option.xAxis[0].columnName = this.nameSelect
          this.currObj._option.xAxis[0].unit = this.xAxisUnit
          this.currObj._option.series = this.seriesList
          this.currObj.option.textStyle = this.font
          this.currObj.option.xAxis.map(item => {
            this.setAxis(item);
            return item;
          })
          this.currObj.option.yAxis.map(item => {
            if (!item.axisLabel) {
              item.axisLabel = {};
            }
            this.setAxis(item);
            return item;
          })
          if (this.font.fontSize === "") {
            delete this.currObj.option.textStyle.fontSize
          }
          if (this.currObj._option.hasOwnProperty('legend')) {
            this.currObj._option.legend = {...this.currObj._option.legend, textStyle: this.font}
          }
          this.$emit('exeParentRefresh');
        }
      },
      EchoDisplay() {
        if (this.currObj._option.tableName !== '') {
          this.dataSelect = this.currObj._option.tableName;
          this.nameSelect = this.currObj._option.xAxis[0].columnName;
          this.xAxisUnit = this.currObj._option.xAxis[0].unit;
          this.seriesList = this.currObj._option.series;
          //赋值给系列列名称判断的时候用
          for (var j = 0; j < this.seriesList.length; j++) {
            this.valueSelect = this.seriesList[j].columnName.colName
          }
        }
      },
      //样式设置
      setFont(font) {
        this.font = font;
      },
      setAxis(item) {
        item.axisLabel["color"] = this.font.color;
        item.axisLabel["fontSize"] = this.font.fontSize;
        item.axisLabel["fontStyle"] = this.font.fontStyle;
        if (this.font.fontSize === "") {
          delete item.axisLabel.fontSize
        }
      }
    },
    created() {
      if (this.currObj.option.hasOwnProperty('textStyle')) {
        this.font = {...this.font, ...this.currObj.option.textStyle}
      }
    },
    mounted() {
      this.EchoDisplay();
    },
    watch: {
      color1(val) {
        this.currObj.option.visualMap.inRange.color[0] = val;
      },
      color2(val) {
        this.currObj.option.visualMap.inRange.color[1] = val;
        this.series[0].color = val;
      },
      dimension(val) {
        this.currObj.option.visualMap.dimension = val;
      }
    }
  }
</script>

<style scoped>

</style>

<template>
  <div class="chart-card" style="width:100%;height: 100%;padding: 5px 10px; border:0px solid #e0e0e0">
    <el-tabs v-model="activeName">
      <el-tab-pane label="数据设置" name="data">
        <el-form label-width="80px">
          <son-chart ref="sonPie" :tableTitle="tableTitle" :params="params" :currObj="currObj"
                     @screenField="screenField" @setDataName="setDataName"
                     :layout="layout"></son-chart>
          <!--坐标&系列-->
          <son-pie-group3 ref="coo" :tableTitle="tableTitle" :params="params" :currObj="currObj"
                                  @exeParentRefresh="exeParentRefresh"></son-pie-group3>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="样式设置" name="style">
        <son-pie-group3-SetUp @exeParentRefresh="exeParentRefresh" :params="params" :currObj="currObj"></son-pie-group3-SetUp>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  import sonChart from './subclass/son-chart';
  import SonPieGroup3SetUp from './subclass/son-pie-group3-SetUp';
  import SonPieGroup3 from './subclass/son-pie-group3';
  // 记录当前card下的所有的组件引用
  export default {
    props: {
      params: Object,
      currObj: Object,
      layout:Array
    },
    data() {
      return {
        activeName: 'data',
        dataSelect: '',
        tableTitle: [],
      }
    },
    components: {
      sonChart: sonChart,
      SonPieGroup3SetUp: SonPieGroup3SetUp,
      SonPieGroup3: SonPieGroup3
    },
    methods: {
      setDataName (name) {
        this.dataSelect = name;
        this.$nextTick(() => {
          this.$refs.coo.DataName(name)
        })
      },
      //找到父级对应的方法进行修改图表
      exeParentRefresh() {
        let com = this;
        let i = 0;
        while (!com._upQueryCurrObj && i < 20) {
          com = com.$parent;
          i++;
        }
        com._upQueryCurrObj(this.currObj);
      },
      //数据设置
      //选中的系列名称下的列名称
      onChange1(val) {
        this.currObj._option.dsSetting.nameColName = val;
      },
      //数据筛选列名列表渲染
      screenField(data) {
        this.$http({
          // url: this.$http.adornUrl('/report/ReportDataSourceitem/uptateBygetItem'),
          url: this.$http.adornUrl('/ichart/dataSourceItem/getDataSourceByNameAndCode'),
          method: 'get',
          params: this.$http.adornParams({
            'tableCode': data,
            'type': "1"
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tableTitle = data.data;
          }
        })
      },
      deleteRow(index, rows) {
        rows.splice(index, 1);
      }
      //样式设置
    },
    created() {
    },
    mounted() {
      this.dataSelect = this.currObj._option.tableName;
    }
  }
</script>
<style>
  .chart-card .el-tabs__item {
    width: 65%;
    text-align: center
  }

  .filter-1 .el-select {
    width: 100%;
  }

  .data-name {
    padding: 5px 10px;
    border-bottom: 1px solid #eee;
    line-height: 30px
  }

  .data-name-title {
    padding: 5px 0px;
    border-bottom: 1px solid #eee;
    color: #000;
    font-size: 16px
  }

  .data-name-con {
    margin: 3% 5%
  }

  .data-name-con label {
    display: inline-block;
    width: 20%
  }
</style>

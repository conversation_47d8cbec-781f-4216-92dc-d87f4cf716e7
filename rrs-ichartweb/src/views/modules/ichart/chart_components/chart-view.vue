<template>
  <div class="dagre-graph-container" :id="containerId">
    
  </div>
</template>
<script>
  /**
   * 流程图
   */
  import * as d3 from 'd3'
  import dagreD3 from 'dagre-d3v4'
  let container = null
  export default {
    name: 'DagreGraph',
    props: ['nodes', 'edges'],
    data() {
      return {
        id: '',
        renderer: null,
        graph: null,
        direction: 'LR',
        directions: [
          {
            prop: 'LR',
            label: '从左至右'
          },
          {
            prop: 'RL',
            label: '从右至左'
          },
          {
            prop: 'TB',
            label: '从上至下'
          },
          {
            prop: 'BT',
            label: '从下至上'
          }
        ],
        zoom: null,
        containerId: '',
        width: 0,
        height: 0,
        isFirst: true
      }
    },
    /*created() {
      this.containerId = this.uuid()
      this.graph = new dagreD3.graphlib.Graph().setGraph({
        rankdir: this.direction
      }).setDefaultEdgeLabel(function () { return {} })
    },*/
    methods: {
      reloadDagreTemp(nodes, edges) {
        this.nodes = nodes
        this.edges = edges
        this.containerId = this.uuid()
        this.graph = new dagreD3.graphlib.Graph().setGraph({
          rankdir: this.direction
        }).setDefaultEdgeLabel(function () { return {} })
        this.$nextTick(() => {
          this.mountedZX()
        })
      },
      uuid () {
        function s4 () {
          return Math.floor((1 + Math.random()) * 0x10000)
            .toString(16)
            .substring(1)
        }
        return (
          s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4()
        )
      },
      zoomCtrl (symbal) {
        let scale = symbal === 'out' ? 1.1 : 0.8
        const svg = d3.select(this.$el).select('svg.dagre')
        this.zoom.scaleBy(svg, scale)
      },
      /**
       * @description control the canvas zoom to up or down
       */
      zoomed () {
        d3.select(this.$el).select('g.container') // 注释并打下一行可拖拽
//        d3.select(this.$el).select('g.container').attr('transform', d3.event.transform)
        /*document.addEventListener("click", (event) => {
          console.log('zcx')
        });*/
      },
      /**
       * @description 画节点
       */
      strokeNodes () {
        // 获取之前的nodes缓存并清除
        let nodes = this.graph.nodes()
        if (nodes.length) {
          nodes.forEach(
            item => {
              this.graph.removeNode(item)
            }
          )
        }
        //通过operator来画shape(BranchPythonMapOperator: 分支； JoinOperator：合流)
        this.nodes.forEach(
          (item) => {
            let state = item.state ? item.state : 'no-status'
            let shape = 'rect'
            this.graph.setNode(item.id, {label: item.label, shape: shape, class: item.class + ' dagre ' + state})
            // this.graph.setNode(item.id, {label: item.value.label, shape: shape, class: item.value.operator + ' dagre ' + state})
          }
        )
        this.renderer(container, this.graph)
      },
      /**
       * @description 画线
       */
      strokeEdges () {
        //一个脚本节点时：不渲染eage
        if (this.nodes.length > 1) {
          this.edges.forEach(
            (item) => {
              if (item.label) {
                this.graph.setEdge(item.from, item.to, {label: item.label})
              } else {
                this.graph.setEdge(item.from, item.to)
              }
            }
          )
          this.renderer(container, this.graph)
        }
      },
      changeWh(wTag) {
        var scaleNum = 1;
        var wg = wTag.width;
        var hg = wTag.height;
        if (wTag.width > this.width || wTag.height > 300) {
          var s1 = setInterval(_ => {
            scaleNum -= 0.1;
            wg = wTag.width * scaleNum;
            hg = wTag.height * scaleNum;
            if (wg <= this.width && hg <= 300) {
              clearInterval(s1)
              document.getElementById('conTag').setAttribute('transform', 'translate(' + parseInt((this.width - wg) / 2) + ',' + parseInt((this.height - hg) / 2) + ') scale(' + scaleNum + ')')
            }
          }, 100)
        } else {
          document.getElementById('conTag').setAttribute('transform', 'translate(' + parseInt((this.width - wg) / 2) + ',' + parseInt((this.height - hg) / 2) + ') scale(' + scaleNum + ')')
        }
      },
      mountedZX() {
        window.d3 = d3
        this.width = document.getElementById(this.containerId).clientWidth
        this.height = document.getElementById(this.containerId).clientHeight
        let d = '0 ' + '0 ' + this.width + ' ' + this.height + ' ';
        // eslint-disable-next-line
        this.renderer = new dagreD3.render()
        const svg = d3.select(this.$el).select('svg.dagre')
//        .attr('width', this.width)
//        .attr('height', this.height)
        .attr('preserveAspectRatio', 'xMidYMid meet')
        .attr('viewBox', d)
        container = svg.select('g.container')

        // const svg2 = d3.select(this.$el).select('svg.dagre')
        // var clientRect = svg2.node().getBoundingClientRect();
          // width = +clientRect.width,
          // height = +clientRect.height;
        // transform
//        const transform = d3.zoomIdentity.translate(this.width / 8, this.height / 10).scale(0.7)
        const transform = d3.zoomIdentity.scale(0.7)
        this.zoom = d3.zoom()
          .scaleExtent([0.5, 0.7]) // 缩放倍数
          //.translateExtent([1, 1], [1, 1])
          .on('zoom', this.zoomed)
        container.transition().duration(750).call(this.zoom.transform, transform)
        svg.call(this.zoom)

        this.strokeNodes()
        this.strokeEdges()
        var wTag = document.getElementById('conTag').getBBox()
        console.log(wTag)
        this.changeWh(wTag)
      }
    },
    mounted () {
      /*window.d3 = d3
      this.width = document.getElementById(this.containerId).clientWidth
      this.height = document.getElementById(this.containerId).clientHeight
      // eslint-disable-next-line
      this.renderer = new dagreD3.render()
      const svg = d3.select(this.$el).select('svg.dagre')
        .attr('width', this.width)1
        .attr('height', this.height)
      container = svg.select('g.container')
      // transform
      const transform = d3.zoomIdentity.translate(this.width / 3, this.height / 6).scale(1)
      this.zoom = d3.zoom()
        .scaleExtent([1 / 2, 8])
        .on('zoom', this.zoomed)
      container.transition().duration(750).call(this.zoom.transform, transform)
      svg.call(this.zoom)

      this.strokeNodes()
      this.strokeEdges()*/
    }
  }
</script>
<style lang="scss">
  /*.dagre-graph-container{*/
    /*position: relative;*/
  /*}*/
  /*svg {*/
    /*position:absolute;*/
    /*left :500px;*/
    /*top:300px;*/
  /*}*/
  .edgePath path {
    stroke: #333;
    fill: #333;
    stroke-width: 1px;
  }
  /************ 图表变量 ***************/
  $fail: #F71832;
  $success: #61b2e4;
  $running: #87d86f;
  $no-status: #fff;
  /**************** dagre 节点图************************/
  g.node.dagre {
    tspan {
      fill: #fff;
      cursor: pointer;
    }
    &.no-status {
      rect {
        stroke: #333;
        fill: #fff;
      }
      ellipse {
        stroke: #333;
        fill: #fff;
      }
      circle {
        stroke: #333;
        fill: #fff;
      }
      tspan {
        fill: #333;
      }
    }
    &.success {
      rect {
        stroke: #fff;
        fill: $success;
      }
      ellipse {
        stroke: #fff;
        fill: $success;
      }
      circle {
        stroke: #fff;
        fill: $success;
      }
    }
    &.failed {
      rect {
        stroke: #EEF7E8;
        fill: $fail;
      }
      ellipse {
        stroke: #fff;
        fill: $fail;
      }
      circle {
        stroke: #fff;
        fill: $fail;
      }
    }
    &.running {
      rect {
        stroke: #fff;
        fill: $running;
      }
      ellipse {
        stroke: #fff;
        fill: $running;
      }
      circle {
        stroke: #fff;
        fill: $running;
      }
    }
  }
  .zoom {
    margin-left: 40px;
  }
</style>

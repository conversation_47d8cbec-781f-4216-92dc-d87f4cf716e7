<template>
  <div class="mod-report-reportforms-push">
    <el-row>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="报告报表名称:">
          <el-input clearable v-model.trim="reportName" placeholder="请输入"></el-input>
        </el-form-item>
<!--        <el-form-item label="类型:">-->
<!--&lt;!&ndash;          <el-select v-model="report" placeholder="请选择" clearable>&ndash;&gt;-->
<!--&lt;!&ndash;            <el-option label="区域一" value="区域一"></el-option>&ndash;&gt;-->
<!--&lt;!&ndash;            <el-option label="区域二" value="区域二"></el-option>&ndash;&gt;-->
<!--&lt;!&ndash;            <el-option label="区域三" value="区域三"></el-option>&ndash;&gt;-->
<!--&lt;!&ndash;          </el-select>&ndash;&gt;-->
<!--          <el-select v-model="report" placeholder="请选择">-->
<!--            <el-option-->
<!--              v-for="item in options"-->
<!--              :key="item.id"-->
<!--              :label="item.reportName"-->
<!--              :value="item.id">-->
<!--            </el-option>-->
<!--          </el-select>-->

<!--        </el-form-item>-->
        <el-form-item>
          <el-button type="primary" @click="seledataSource(reportName,report)">查询</el-button>
          <el-button v-if="isAuth('report:reportforms-push:xinzeng')" type="primary" @click="preview(1)">新增</el-button>
        </el-form-item>
      </el-form>
    </el-row>
    <el-row class="row-bg row-query-separator"></el-row>
    <el-row>
      <el-table
        :data="page.list"
        ref="multipleTable"
        tooltip-effect="dark"
        style="width: 100%"
        border
        :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
      >

        <el-table-column
          type="index"
          label="序号"
          width="60%">
        </el-table-column>

        <el-table-column
          prop="reportPushName"
          label="名称"
          width=""
        >
        </el-table-column>

        <el-table-column
          label="类型"
          prop="reportName"
          width="">
        </el-table-column>

        <el-table-column
          label="创建时间"
          prop="createTime"
          width="160%">
        </el-table-column>

<!--        <el-table-column-->
<!--          label="绑定规则"-->
<!--          prop="bindingRules"-->
<!--          width="">-->
<!--        </el-table-column>-->

        <el-table-column
          prop="state"
          label="状态"
          :formatter="typeFormatter"
          width="60%">
        </el-table-column>

        <el-table-column label="操作" width="170%">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              v-if="scope.row.state==2 && isAuth('report:reportforms-push:qiyong')"
              @click="updateState(scope.row,1)">启用
            </el-button>

            <el-button
              type="text"
              size="mini"
              v-if="scope.row.state==1 && isAuth('report:reportforms-push:tingyong')"
              @click="updateState(scope.row,2)">禁用
            </el-button>

            <el-button
              type="text"
              size="mini"
              v-if="isAuth('report:reportforms-push:bianji')"
              @click="preview(scope.row)">编辑
            </el-button>

            <el-button
              type="text"
              size="mini"
              v-if="isAuth('report:RptReportPushItem:updateState')"
              @click="deleteItem(scope.row,'0')">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <el-pagination
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
      :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
  export default {
    name: "reportforms",
    data() {
      return {
        dialogVisible: false,
        formInline: {
          user: '',
          region: ''
        },
        page: [],
        dataSource: {
          fieldName: '',
          disableName: '',
          value: ''
        },
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        tableList: [],
        reportName: '',
        ableCode: '',
        report: ''
      }
    },
    activated() {
      this.seledataSource()
    },
    methods: {
      typeFormatter(row, column, cellValue, index) {
        return row.state === 1 ? '启用' : '禁用'
      },
      updateState(val, state) {
        if (state === 1) {
          val.state = 1
        }
        if (state === 2) {
          val.state = 2
        }
        this.$http({
          url: this.$http.adornUrl('/report/RptReportPushItem/updateState'),
          method: 'post',
          data: this.$http.adornParams(val)
        }).then(({data}) => {
          if (data && data.code === 0) {
            if (state === 1) {
              this.$message({
                message: '数据状态已更改为启用',
                type: 'success'
              });
            } else {
              this.$message({
                message: '数据状态已更改为禁用',
                type: 'success'
              });
            }
            this.seledataSource()
          }
        })
      },
      deleteItem(val, state) {
        this.$confirm('确认是否删除该项?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.delRes(val);
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      delRes(val) {
        val.deleteFlag = 0
        this.$http({
          url: this.$http.adornUrl('/report/RptReportPushItem/updateState'),
          method: 'post',
          data: this.$http.adornParams(val)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '数据删除成功!',
              type: 'success'
            });
            this.seledataSource()
          }
        })
      },
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.seledataSource();
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.seledataSource();
      },
      //添加数据  开关
      addDataSource() {
        this.dialogVisible = true;
      },
      //点击打开新页面  并传值
      preview(val, type) {
        if (val.id) {
          var StrpushType = val.pushType
          val.pushType = StrpushType.split(",");
        }
        this.$router.push({name: 'reportformsPushUpdate', params: {data: val}});
      },
      seledataSource(val, value) {
        this.$http({
          url: this.$http.adornUrl('/report/RptReportPushItem/getAll'),
          method: 'get',
          params: this.$http.adornParams({
            'page': this.pageIndex,
            'limit': this.pageSize,
            'reportName': this.reportName
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.page = data.page;
            this.totalPage = data.page.totalCount
          }
        })
      }
    },
    created() {
      this.seledataSource()
    }
  }
</script>

<style>

</style>
